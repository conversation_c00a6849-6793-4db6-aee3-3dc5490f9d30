#!/usr/bin/env node

/**
 * RBAC Role Assignment Monitor
 * 
 * This script monitors for users who should have RBAC roles but don't,
 * and automatically assigns the appropriate default roles.
 * 
 * Run with: node monitor-rbac-assignments.js
 * Or add to cron job for periodic monitoring
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Database configuration - use environment variable or fallback
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/myprofile';

// Define schemas
const userSchema = new Schema({
  email: String,
  fullName: String,
  username: String,
  role: {
    type: String,
    enum: ['superadmin', 'admin', 'user'],
    default: 'user'
  },
  isEmailVerified: Boolean,
  isPhoneVerified: Boolean,
  accountType: String,
  accountCategory: String,
  isBanned: Boolean,
  isAccountLocked: <PERSON>olean,
  createdAt: Date,
  updatedAt: Date
}, { timestamps: true });

const userRoleSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User' },
  roleType: {
    type: String,
    enum: RoleTypeValues
  },
  assignedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  assignedAt: { type: Date, default: Date.now },
  expiresAt: Date,
  isActive: { type: Boolean, default: true },
  restrictions: {
    ipWhitelist: [String],
    timeRestrictions: {
      allowedHours: {
        start: { type: Number, min: 0, max: 23 },
        end: { type: Number, min: 0, max: 23 }
      },
      allowedDays: [{ type: Number, min: 0, max: 6 }],
      timezone: String
    },
    departmentRestrictions: [String],
    resourceRestrictions: [String]
  },
  metadata: {
    reason: String,
    approvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    temporary: { type: Boolean, default: false },
    emergencyAccess: { type: Boolean, default: false }
  },
  auditTrail: [{
    action: {
      type: String,
      enum: ['assigned', 'modified', 'suspended', 'reactivated', 'expired'],
      required: true
    },
    performedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    performedAt: { type: Date, default: Date.now },
    reason: String,
    previousState: Schema.Types.Mixed
  }]
}, { timestamps: true });

// Import RoleType enum values
const RoleTypeValues = [
  'super_admin', 'supra_admin', 'major_admin', 'admin_user', 'proxy_admin',
  'regular_user', 'merchant', 'guest', 'beta_tester', 'content_moderator',
  'finance_auditor', 'developer'
];

const roleSchema = new Schema({
  name: {
    type: String,
    enum: RoleTypeValues
  },
  displayName: String,
  description: String,
  permissions: [String],
  isActive: Boolean
}, { timestamps: true });

// Models
const User = mongoose.model('User', userSchema);
const UserRole = mongoose.model('UserRole', userRoleSchema);
const Role = mongoose.model('Role', roleSchema);

async function monitorRBACAssignments() {
  try {
    console.log('🔍 RBAC Role Assignment Monitor Starting...');
    console.log('=' .repeat(50));
    
    // Connect to database
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database');

    // Find system admin user to use as assignedBy
    const systemAdmin = await User.findOne({ 
      role: { $in: ['superadmin', 'admin'] } 
    }).sort({ createdAt: 1 });

    let assignedBy;
    if (!systemAdmin) {
      console.log('⚠️  No admin user found, looking for any user...');
      const firstUser = await User.findOne().sort({ createdAt: 1 });
      if (!firstUser) {
        throw new Error('No users found in database');
      }
      assignedBy = firstUser._id;
      console.log(`📋 Using first user ${firstUser.email || firstUser.username || 'unknown'} as role assigner`);
    } else {
      assignedBy = systemAdmin._id;
      console.log(`📋 Using admin user ${systemAdmin.email || systemAdmin.username || 'unknown'} as role assigner`);
    }

    // Get users with active RBAC roles
    const usersWithRBACRoles = await UserRole.aggregate([
      {
        $match: {
          isActive: true,
          $or: [
            { expiresAt: { $exists: false } },
            { expiresAt: null },
            { expiresAt: { $gt: new Date() } }
          ]
        }
      },
      {
        $group: {
          _id: '$userId',
          roles: { $push: '$roleType' }
        }
      }
    ]);

    const userIdsWithRBAC = usersWithRBACRoles.map(u => u._id.toString());

    // Find users without any RBAC roles (only regular users, not banned/locked)
    const usersWithoutRBAC = await User.find({
      _id: { $nin: userIdsWithRBAC.map(id => new mongoose.Types.ObjectId(id)) },
      role: 'user', // Only regular users
      isBanned: { $ne: true },
      isAccountLocked: { $ne: true },
      isEmailVerified: true // Only verified users
    }).select('_id email fullName username createdAt');

    console.log(`\n📊 Found ${usersWithoutRBAC.length} users without RBAC roles`);

    if (usersWithoutRBAC.length === 0) {
      console.log('✅ All eligible users have RBAC roles assigned!');
      return { fixed: 0, errors: 0 };
    }

    console.log('\n🔧 Auto-fixing missing role assignments...');
    
    let fixedCount = 0;
    let errorCount = 0;
    const errors = [];

    for (const user of usersWithoutRBAC) {
      try {
        // Check if role was just assigned (race condition protection)
        const recentRole = await UserRole.findOne({
          userId: user._id,
          isActive: true
        });
        
        if (recentRole) {
          console.log(`⏭️  User ${user.email} already has role ${recentRole.roleType}, skipping`);
          continue;
        }

        // Assign regular_user role
        const userRole = new UserRole({
          userId: user._id,
          roleType: 'regular_user',
          assignedBy: assignedBy,
          assignedAt: new Date(),
          isActive: true,
          metadata: {
            reason: 'Automatic assignment by RBAC monitor - missing role detected',
            temporary: false,
            emergencyAccess: false
          },
          auditTrail: [{
            action: 'assigned',
            performedBy: assignedBy,
            performedAt: new Date(),
            reason: 'Auto-fix by RBAC assignment monitor'
          }]
        });

        await userRole.save();
        console.log(`✅ Fixed: ${user.email} now has regular_user role`);
        fixedCount++;

      } catch (error) {
        console.log(`❌ Failed to fix ${user.email}: ${error.message}`);
        errors.push({ user: user.email, error: error.message });
        errorCount++;
      }
    }

    console.log(`\n📈 Monitor Results:`);
    console.log(`✅ Fixed: ${fixedCount} users`);
    console.log(`❌ Errors: ${errorCount} users`);

    if (errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      errors.forEach((err, index) => {
        console.log(`${index + 1}. ${err.user}: ${err.error}`);
      });
    }

    return { fixed: fixedCount, errors: errorCount };

  } catch (error) {
    console.error('❌ Monitor error:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run the monitor
if (require.main === module) {
  monitorRBACAssignments()
    .then((result) => {
      console.log('\n🎉 RBAC monitoring completed successfully!');
      if (result.fixed > 0) {
        console.log(`📧 Consider checking logs for ${result.fixed} users who had missing roles.`);
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Monitor failed:', error);
      process.exit(1);
    });
}

module.exports = { monitorRBACAssignments }; 