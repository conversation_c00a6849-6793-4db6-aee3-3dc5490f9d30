#!/usr/bin/env node

/**
 * Fix Admin Super Admin Role Assignment
 * 
 * This script ensures admin users have the SUPER_ADMIN role,
 * giving them the fullest permissions in the system.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Database configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/myprofile';

// Define schemas
const userSchema = new Schema({
  email: String,
  fullName: String,
  username: String,
  role: {
    type: String,
    enum: ['superadmin', 'admin', 'user'],
    default: 'user'
  },
  isEmailVerified: Boolean,
  isPhoneVerified: Boolean,
  accountType: String,
  accountCategory: String,
  isBanned: Boolean,
  isAccountLocked: Boolean,
  createdAt: Date,
  updatedAt: Date
}, { timestamps: true });

// Import RoleType enum values
const RoleTypeValues = [
  'super_admin', 'supra_admin', 'major_admin', 'admin_user', 'proxy_admin',
  'regular_user', 'merchant', 'guest', 'beta_tester', 'content_moderator',
  'finance_auditor', 'developer'
];

const userRoleSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User' },
  roleType: {
    type: String,
    enum: RoleTypeValues
  },
  assignedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  assignedAt: { type: Date, default: Date.now },
  expiresAt: Date,
  isActive: { type: Boolean, default: true },
  restrictions: {
    ipWhitelist: [String],
    timeRestrictions: {
      allowedHours: {
        start: { type: Number, min: 0, max: 23 },
        end: { type: Number, min: 0, max: 23 }
      },
      allowedDays: [{ type: Number, min: 0, max: 6 }],
      timezone: String
    },
    departmentRestrictions: [String],
    resourceRestrictions: [String]
  },
  metadata: {
    reason: String,
    approvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    temporary: { type: Boolean, default: false },
    emergencyAccess: { type: Boolean, default: false }
  },
  auditTrail: [{
    action: {
      type: String,
      enum: ['assigned', 'modified', 'suspended', 'reactivated', 'expired'],
      required: true
    },
    performedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    performedAt: { type: Date, default: Date.now },
    reason: String,
    previousState: Schema.Types.Mixed
  }]
}, { timestamps: true });

// Models
const User = mongoose.model('User', userSchema);
const UserRole = mongoose.model('UserRole', userRoleSchema);

async function fixAdminSuperAdminRole() {
  try {
    console.log('🔧 Ensuring Admin Users Have SUPER_ADMIN Role...');
    console.log('=' .repeat(55));
    
    // Connect to database
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database');

    // Find admin users
    const adminUsers = await User.find({
      role: { $in: ['admin', 'superadmin'] }
    }).select('_id email fullName username role');

    console.log(`\n📋 Found ${adminUsers.length} admin users to check`);

    if (adminUsers.length === 0) {
      console.log('ℹ️  No admin users found');
      return { fixed: 0, errors: 0 };
    }

    let fixedCount = 0;
    let errorCount = 0;
    const errors = [];

    for (const adminUser of adminUsers) {
      try {
        console.log(`\n🔍 Checking admin user: ${adminUser.email} (${adminUser.role})`);

        // Check if user already has super_admin role
        const existingSuperAdminRole = await UserRole.findOne({
          userId: adminUser._id,
          roleType: 'super_admin',
          isActive: true
        });

        if (existingSuperAdminRole) {
          console.log(`  ✅ User already has SUPER_ADMIN role, skipping`);
          continue;
        }

        // Get existing roles for this admin user
        const existingRoles = await UserRole.find({
          userId: adminUser._id,
          isActive: true
        }).select('roleType');

        const roleTypes = existingRoles.map(r => r.roleType);
        console.log(`  📊 Current RBAC roles: ${roleTypes.join(', ')}`);

        // Use the admin user themselves as the assigner
        const assignedBy = adminUser._id;

        // Assign super_admin role
        const userRole = new UserRole({
          userId: adminUser._id,
          roleType: RoleTypeValues[0], // 'super_admin'
          assignedBy: assignedBy,
          assignedAt: new Date(),
          isActive: true,
          metadata: {
            reason: 'Ensuring admin user has SUPER_ADMIN role for complete system access',
            temporary: false,
            emergencyAccess: false
          },
          auditTrail: [{
            action: 'assigned',
            performedBy: assignedBy,
            performedAt: new Date(),
            reason: 'Admin super admin role assignment - ensuring fullest permissions'
          }]
        });

        await userRole.save();
        console.log(`  ✅ Added SUPER_ADMIN role to ${adminUser.email}`);
        fixedCount++;

      } catch (error) {
        console.log(`  ❌ Failed to fix ${adminUser.email}: ${error.message}`);
        errors.push({ user: adminUser.email, error: error.message });
        errorCount++;
      }
    }

    console.log(`\n📈 Fix Results:`);
    console.log(`✅ Fixed: ${fixedCount} admin users`);
    console.log(`❌ Errors: ${errorCount} admin users`);

    if (errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      errors.forEach((err, index) => {
        console.log(`${index + 1}. ${err.user}: ${err.error}`);
      });
    }

    return { fixed: fixedCount, errors: errorCount };

  } catch (error) {
    console.error('❌ Fix error:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run the fix
if (require.main === module) {
  fixAdminSuperAdminRole()
    .then((result) => {
      console.log('\n🎉 Admin SUPER_ADMIN role assignment completed successfully!');
      if (result.fixed > 0) {
        console.log(`🚀 Successfully ensured ${result.fixed} admin users have SUPER_ADMIN role.`);
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fix failed:', error);
      process.exit(1);
    });
}

module.exports = { fixAdminSuperAdminRole }; 