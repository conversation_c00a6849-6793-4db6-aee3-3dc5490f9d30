#!/usr/bin/env node

/**
 * Check Non-Regular Users Script
 * 
 * This script finds all accounts that do not have the regular_user role,
 * checking both legacy roles (User.role) and RBAC system (UserRole).
 */

// load environment variables
require('dotenv').config();

// import mongoose
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Database configuration - update with your connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/myprofile';

// Define schemas based on the codebase
const userSchema = new Schema({
  email: String,
  fullName: String,
  username: String,
  role: {
    type: String,
    enum: ['superadmin', 'admin', 'user'],
    default: 'user'
  },
  isEmailVerified: Boolean,
  isPhoneVerified: Boolean,
  accountType: String,
  accountCategory: String,
  isBanned: Boolean,
  isAccountLocked: <PERSON><PERSON><PERSON>,
  createdAt: Date,
  updatedAt: Date
}, { timestamps: true });

// Import RoleType enum values
const RoleTypeValues = [
  'super_admin', 'supra_admin', 'major_admin', 'admin_user', 'proxy_admin',
  'regular_user', 'merchant', 'guest', 'beta_tester', 'content_moderator',
  'finance_auditor', 'developer'
];

const userRoleSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User' },
  roleType: {
    type: String,
    enum: RoleTypeValues
  },
  isActive: Boolean,
  assignedAt: Date,
  expiresAt: Date,
  assignedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  metadata: {
    reason: String,
    temporary: Boolean,
    emergencyAccess: Boolean
  }
}, { timestamps: true });

const roleSchema = new Schema({
  name: {
    type: String,
    enum: RoleTypeValues
  },
  displayName: String,
  description: String,
  permissions: [String],
  isActive: Boolean
}, { timestamps: true });

// Models
const User = mongoose.model('User', userSchema);
const UserRole = mongoose.model('UserRole', userRoleSchema);
const Role = mongoose.model('Role', roleSchema);

async function checkNonRegularUsers() {
  try {
    console.log('🔌 Connecting to database...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database successfully\n');

    console.log('📊 ANALYSIS: Accounts without regular_user permissions\n');
    console.log('=' * 80);

    // 1. Check legacy role system - users without 'user' role
    console.log('\n1️⃣ LEGACY ROLE SYSTEM ANALYSIS');
    console.log('-' * 40);
    
    const nonRegularLegacyUsers = await User.find({
      role: { $ne: 'user' }
    }).select('_id email fullName username role isEmailVerified accountType createdAt isBanned isAccountLocked');

    console.log(`Found ${nonRegularLegacyUsers.length} users with non-regular legacy roles:`);
    
    if (nonRegularLegacyUsers.length > 0) {
      console.log('\nNon-regular legacy users:');
      nonRegularLegacyUsers.forEach((user, index) => {
        console.log(`${index + 1}. Email: ${user.email}`);
        console.log(`   Full Name: ${user.fullName}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Legacy Role: ${user.role}`);
        console.log(`   Account Type: ${user.accountType}`);
        console.log(`   Email Verified: ${user.isEmailVerified}`);
        console.log(`   Banned: ${user.isBanned || false}`);
        console.log(`   Locked: ${user.isAccountLocked || false}`);
        console.log(`   Created: ${user.createdAt}`);
        console.log(`   User ID: ${user._id}`);
        console.log('');
      });
    }

    // 2. Check RBAC system - users without regular_user role type
    console.log('\n2️⃣ RBAC SYSTEM ANALYSIS');
    console.log('-' * 40);

    // Get all users with RBAC roles
    const usersWithRBACRoles = await UserRole.aggregate([
      {
        $match: {
          isActive: true,
          $or: [
            { expiresAt: { $exists: false } },
            { expiresAt: null },
            { expiresAt: { $gt: new Date() } }
          ]
        }
      },
      {
        $group: {
          _id: '$userId',
          roles: { $push: '$roleType' },
          assignedDates: { $push: '$assignedAt' }
        }
      }
    ]);

    console.log(`Found ${usersWithRBACRoles.length} users with active RBAC roles`);

    // Find users without regular_user role in RBAC
    const usersWithoutRegularUser = usersWithRBACRoles.filter(user => 
      !user.roles.includes('regular_user')
    );

    console.log(`Found ${usersWithoutRegularUser.length} users without regular_user RBAC role:`);

    if (usersWithoutRegularUser.length > 0) {
      console.log('\nUsers without regular_user RBAC role:');
      
      for (let i = 0; i < usersWithoutRegularUser.length; i++) {
        const rbacUser = usersWithoutRegularUser[i];
        const userDetails = await User.findById(rbacUser._id).select('email fullName username role accountType isEmailVerified createdAt isBanned isAccountLocked');
        
        if (userDetails) {
          console.log(`${i + 1}. Email: ${userDetails.email}`);
          console.log(`   Full Name: ${userDetails.fullName}`);
          console.log(`   Username: ${userDetails.username}`);
          console.log(`   Legacy Role: ${userDetails.role}`);
          console.log(`   RBAC Roles: ${rbacUser.roles.join(', ')}`);
          console.log(`   Account Type: ${userDetails.accountType}`);
          console.log(`   Email Verified: ${userDetails.isEmailVerified}`);
          console.log(`   Banned: ${userDetails.isBanned || false}`);
          console.log(`   Locked: ${userDetails.isAccountLocked || false}`);
          console.log(`   Created: ${userDetails.createdAt}`);
          console.log(`   User ID: ${userDetails._id}`);
          console.log('');
        }
      }
    }

    // 3. Check users with no RBAC roles at all
    console.log('\n3️⃣ USERS WITHOUT ANY RBAC ROLES');
    console.log('-' * 40);

    const allUserIds = await User.find().distinct('_id');
    const userIdsWithRBAC = usersWithRBACRoles.map(u => u._id.toString());
    
    const userIdsWithoutRBAC = allUserIds.filter(userId => 
      !userIdsWithRBAC.includes(userId.toString())
    );

    console.log(`Found ${userIdsWithoutRBAC.length} users with no RBAC roles assigned:`);

    if (userIdsWithoutRBAC.length > 0) {
      console.log('\nUsers without any RBAC roles:');
      
      for (let i = 0; i < userIdsWithoutRBAC.length; i++) {
        const userId = userIdsWithoutRBAC[i];
        const userDetails = await User.findById(userId).select('email fullName username role accountType isEmailVerified createdAt isBanned isAccountLocked');
        
        if (userDetails) {
          console.log(`${i + 1}. Email: ${userDetails.email}`);
          console.log(`   Full Name: ${userDetails.fullName}`);
          console.log(`   Username: ${userDetails.username}`);
          console.log(`   Legacy Role: ${userDetails.role}`);
          console.log(`   RBAC Roles: None assigned`);
          console.log(`   Account Type: ${userDetails.accountType}`);
          console.log(`   Email Verified: ${userDetails.isEmailVerified}`);
          console.log(`   Banned: ${userDetails.isBanned || false}`);
          console.log(`   Locked: ${userDetails.isAccountLocked || false}`);
          console.log(`   Created: ${userDetails.createdAt}`);
          console.log(`   User ID: ${userDetails._id}`);
          console.log('');
        }
      }
    }

    // 4. Summary and role permissions analysis
    console.log('\n4️⃣ ROLE PERMISSIONS ANALYSIS');
    console.log('-' * 40);

    // Get regular_user role permissions
    const regularUserRole = await Role.findOne({ name: 'regular_user' });
    if (regularUserRole) {
      console.log('\nRegular User Role Permissions:');
      console.log(`Role: ${regularUserRole.displayName}`);
      console.log(`Description: ${regularUserRole.description}`);
      console.log(`Permissions (${regularUserRole.permissions.length}):`, regularUserRole.permissions.join(', '));
    } else {
      console.log('\n⚠️  Warning: regular_user role not found in RBAC system!');
    }

    // Get all roles and their permissions for comparison
    const allRoles = await Role.find({ isActive: true }).select('name displayName permissions');
    console.log(`\nAll available roles (${allRoles.length}):`);
    allRoles.forEach(role => {
      console.log(`- ${role.name} (${role.displayName}): ${role.permissions.length} permissions`);
    });

    // 5. Final Summary
    console.log('\n5️⃣ SUMMARY');
    console.log('-' * 40);
    console.log(`Total users in database: ${allUserIds.length}`);
    console.log(`Users with non-regular legacy roles: ${nonRegularLegacyUsers.length}`);
    console.log(`Users without regular_user RBAC role: ${usersWithoutRegularUser.length}`);
    console.log(`Users with no RBAC roles: ${userIdsWithoutRBAC.length}`);
    
    const totalNonRegular = new Set([
      ...nonRegularLegacyUsers.map(u => u._id.toString()),
      ...usersWithoutRegularUser.map(u => u._id.toString()),
      ...userIdsWithoutRBAC.map(u => u.toString())
    ]).size;
    
    console.log(`Unique users without regular_user permissions: ${totalNonRegular}`);

    console.log('\n✅ Analysis completed successfully!');

  } catch (error) {
    console.error('❌ Error during analysis:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from database');
  }
}

// Handle command line execution
if (require.main === module) {
  checkNonRegularUsers().catch(console.error);
}

module.exports = checkNonRegularUsers; 