import { Request, Response, NextFunction } from 'express';
import { RBACService } from '../services/rbac.service';
import { RoleType, RoleLevel } from '../models/Role';
import { User } from '../models/User';
import { logger } from '../utils/logger';
import { CustomError } from '../utils/errors';

// Legacy role type for backward compatibility
type LegacyRole = 'superadmin' | 'admin' | 'user';

// Enhanced request interface with RBAC context
declare global {
  namespace Express {
    interface Request {
      rbacContext?: {
        permissions: string[];
        roles: RoleType[];
        highestRoleLevel: RoleLevel;
        ipAddress: string;
      };
    }
  }
}

/**
 * Enhanced role-based access control middleware with backward compatibility
 */
export class RBACMiddleware {

  /**
   * Check if user has specific permission(s)
   * This is the new granular permission system
   */
  static requirePermission(
    permissions: string | string[],
    options?: {
      requireAll?: boolean; // Default: false (require any)
      context?: { resourceId?: string };
    }
  ) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          throw new CustomError('UNAUTHORIZED', 'Authentication required');
        }

        // COMPLETE ADMIN BYPASS - Skip all RBAC checks for admin users
        const isLegacyAdmin = user.role === RoleType.ADMIN_USER || user.role === RoleType.SUPER_ADMIN;
        if (isLegacyAdmin) {
          return next(); // Admin users bypass all RBAC restrictions
        }

        // Check RBAC admin roles as well
        try {
          const userLevel = await RBACService.getUserHighestRoleLevel(user._id);
          const isRBACAdmin = userLevel >= RoleLevel.PROXY_ADMIN; // Level 30 and above
          if (isRBACAdmin) {
            return next(); // RBAC admin users bypass all restrictions
          }
        } catch (error) {
          // If RBAC check fails, continue with legacy admin check
          if (isLegacyAdmin) {
            return next();
          }
        }

        const permissionList = Array.isArray(permissions) ? permissions : [permissions];
        const requireAll = options?.requireAll || false;
        const ipAddress = req.ip || req.connection.remoteAddress || '';

        const context = {
          resourceId: options?.context?.resourceId || req.params.id || req.params.userId,
          ipAddress,
          additionalConditions: {}
        };

        let hasAccess = false;

        if (requireAll) {
          hasAccess = await RBACService.hasAllPermissions(user._id, permissionList, context);
        } else {
          hasAccess = await RBACService.hasAnyPermission(user._id, permissionList, context);
        }

        if (!hasAccess) {
          logger.warn(`Permission denied for user ${user._id}`, {
            userId: user._id,
            requiredPermissions: permissionList,
            requireAll,
            ipAddress
          });
          throw new CustomError('FORBIDDEN', 'Insufficient permissions');
        }

        // Cache RBAC context for this request
        if (!req.rbacContext) {
          req.rbacContext = {
            permissions: await RBACService.getUserPermissions(user._id),
            roles: (await RBACService.getUserActiveRoles(user._id)).map(ur => ur.roleType),
            highestRoleLevel: await RBACService.getUserHighestRoleLevel(user._id),
            ipAddress
          };
        }

        next();
      } catch (error) {
        logger.error('RBAC permission check error:', error);
        res.status(error instanceof CustomError ? 403 : 500).json({
          success: false,
          message: error instanceof Error ? error.message : 'Permission denied'
        });
      }
    };
  }

  /**
   * Check if user has specific role(s) - New RBAC system
   */
  static requireNewRole(roles: RoleType | RoleType[]) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          throw new CustomError('UNAUTHORIZED', 'Authentication required');
        }

        const roleList = Array.isArray(roles) ? roles : [roles];
        const hasRole = await RBACService.hasRole(user._id, roleList);

        if (!hasRole) {
          logger.warn(`Role access denied for user ${user._id}`, {
            userId: user._id,
            requiredRoles: roleList,
            userRoles: (await RBACService.getUserActiveRoles(user._id)).map(ur => ur.roleType)
          });
          throw new CustomError('FORBIDDEN', 'Insufficient role privileges');
        }

        next();
      } catch (error) {
        logger.error('RBAC role check error:', error);
        res.status(error instanceof CustomError ? 403 : 500).json({
          success: false,
          message: error instanceof Error ? error.message : 'Access denied'
        });
      }
    };
  }

  /**
   * Check minimum role level requirement
   */
  static requireMinimumLevel(minimumLevel: RoleLevel) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          throw new CustomError('UNAUTHORIZED', 'Authentication required');
        }

        // COMPLETE ADMIN BYPASS - Skip all level checks for admin users
        const isLegacyAdmin = user.role === 'admin' || user.role === 'superadmin';
        if (isLegacyAdmin) {
          return next(); // Admin users bypass all level restrictions
        }

        // Check RBAC admin roles as well
        try {
          const userLevel = await RBACService.getUserHighestRoleLevel(user._id);
          const isRBACAdmin = userLevel >= RoleLevel.PROXY_ADMIN; // Level 30 and above
          if (isRBACAdmin) {
            return next(); // RBAC admin users bypass all restrictions
          }
        } catch (error) {
          // If RBAC check fails, continue with legacy admin check
          if (isLegacyAdmin) {
            return next();
          }
        }

        const hasLevel = await RBACService.hasMinimumRoleLevel(user._id, minimumLevel);

        if (!hasLevel) {
          const userLevel = await RBACService.getUserHighestRoleLevel(user._id);
          logger.warn(`Role level access denied for user ${user._id}`, {
            userId: user._id,
            requiredLevel: minimumLevel,
            userLevel
          });
          throw new CustomError('FORBIDDEN', 'Insufficient role level');
        }

        next();
      } catch (error) {
        logger.error('RBAC role level check error:', error);
        res.status(error instanceof CustomError ? 403 : 500).json({
          success: false,
          message: error instanceof Error ? error.message : 'Access denied'
        });
      }
    };
  }

  /**
   * LEGACY COMPATIBILITY: Enhanced version of existing requireRole middleware
   * Supports both legacy role system and new RBAC system
   */
  static requireRole(roles: LegacyRole | LegacyRole[]) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          throw new CustomError('UNAUTHORIZED', 'Authentication required');
        }

        // COMPLETE ADMIN BYPASS - Skip all role checks for admin users
        const isLegacyAdmin = user.role === RoleType.ADMIN_USER || user.role === RoleType.SUPER_ADMIN;
        if (isLegacyAdmin) {
          return next(); // Admin users bypass all role restrictions
        }

        // Check RBAC admin roles as well
        try {
          const userLevel = await RBACService.getUserHighestRoleLevel(user._id);
          const isRBACAdmin = userLevel >= RoleLevel.PROXY_ADMIN; // Level 30 and above
          if (isRBACAdmin) {
            return next(); // RBAC admin users bypass all restrictions
          }
        } catch (error) {
          // If RBAC check fails, continue with legacy admin check
          if (isLegacyAdmin) {
            return next();
          }
        }

        const roleList = Array.isArray(roles) ? roles : [roles];

        // First, check legacy role system (for immediate backward compatibility)
        let hasLegacyAccess = false;
        let userRole = user.role;

        // Try to get role from _doc if it exists (existing logic)
        if (!userRole && (user as any)._doc && (user as any)._doc.role) {
          userRole = (user as any)._doc.role;
        }

        // Check for admin role in headers or cookies (existing logic)
        const adminRoleHeader = req.header('X-User-Role');
        const adminCookie = req.cookies?.['X-User-Role'];
        const isAdminHeader = req.header('X-Is-Admin');
        const isAdminCookie = req.cookies?.['X-Is-Admin'];

        const isAdminFlagHeader = isAdminHeader === 'true' || req.header('X-Is-Admin') === 'true';
        const isAdminFlagCookie = isAdminCookie === 'true' || req.cookies?.['X-Is-Admin'] === 'true';

        // If any admin indicator is present, set role to admin (existing logic)
        if (isAdminHeader || isAdminCookie || isAdminFlagHeader || isAdminFlagCookie) {
          userRole = 'admin';
        }

        // Default to 'user' role if none is specified (existing logic)
        if (!userRole) {
          userRole = 'user';
        }

        // Check legacy role access
        hasLegacyAccess = roleList.includes(userRole as LegacyRole);

        // Also check new RBAC system for enhanced compatibility
        let hasRBACAccess = false;
        try {
          for (const legacyRole of roleList) {
            if (await RBACService.hasLegacyRole(user._id, legacyRole)) {
              hasRBACAccess = true;
              break;
            }
          }
        } catch (rbacError) {
          logger.warn('RBAC legacy role check failed, falling back to legacy system', rbacError);
          hasRBACAccess = false;
        }

        // Grant access if either system grants it
        const hasAccess = hasLegacyAccess || hasRBACAccess;

        if (!hasAccess) {
          logger.error(`Role verification failed: User role '${userRole}' not in allowed roles [${roleList.join(', ')}]`);
          throw new CustomError('FORBIDDEN', 'Insufficient permissions');
        }

        // Cache RBAC context for this request if RBAC system is available
        if (hasRBACAccess && !req.rbacContext) {
          try {
            req.rbacContext = {
              permissions: await RBACService.getUserPermissions(user._id),
              roles: (await RBACService.getUserActiveRoles(user._id)).map(ur => ur.roleType),
              highestRoleLevel: await RBACService.getUserHighestRoleLevel(user._id),
              ipAddress: req.ip || req.connection.remoteAddress || ''
            };
          } catch (contextError) {
            logger.warn('Failed to set RBAC context:', contextError);
          }
        }

        next();
      } catch (error) {
        logger.error('Role verification error:', error);
        res.status(error instanceof CustomError ? 403 : 500).json({
          success: false,
          message: error instanceof Error ? error.message : 'Permission denied'
        });
      }
    };
  }

  /**
   * Check resource ownership (enhanced with RBAC)
   */
  static requireOwnership(options?: {
    allowAdminOverride?: boolean;
    requiredPermission?: string;
    resourceField?: string; // Field containing the owner ID (default: 'userId' or 'createdBy')
  }) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          throw new CustomError('UNAUTHORIZED', 'Authentication required');
        }

        const resourceId = req.params.id || req.params.resourceId;
        const allowAdminOverride = options?.allowAdminOverride !== false; // Default: true
        const requiredPermission = options?.requiredPermission;
        const resourceField = options?.resourceField || 'userId';

        // If admin override is allowed, check if user has admin privileges
        if (allowAdminOverride) {
          // Check legacy admin roles
          const userRole = user.role || ((user as any)._doc ? (user as any)._doc.role : null);
          if (userRole === 'admin' || userRole === 'superadmin') {
            return next();
          }

          // Check new RBAC admin roles
          const hasAdminRole = await RBACService.hasMinimumRoleLevel(user._id, RoleLevel.PROXY_ADMIN);
          if (hasAdminRole) {
            return next();
          }
        }

        // Check specific permission if provided
        if (requiredPermission) {
          const hasPermission = await RBACService.hasPermission(user._id, requiredPermission, {
            resourceId,
            ipAddress: req.ip || req.connection.remoteAddress || ''
          });
          if (hasPermission) {
            return next();
          }
        }

        // Check resource ownership
        // This would need to be customized based on your specific models
        // For now, we'll assume the resource has a userId or createdBy field

        throw new CustomError('FORBIDDEN', 'You do not have permission to access this resource');
      } catch (error) {
        logger.error('Ownership verification error:', error);
        res.status(error instanceof CustomError ? 403 : 500).json({
          success: false,
          message: error instanceof Error ? error.message : 'Access denied'
        });
      }
    };
  }

  /**
   * Middleware to populate RBAC context for a request
   */
  static populateRBACContext() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          return next(); // No user, no context
        }

        const ipAddress = req.ip || req.connection.remoteAddress || '';

        req.rbacContext = {
          permissions: await RBACService.getUserPermissions(user._id),
          roles: (await RBACService.getUserActiveRoles(user._id)).map(ur => ur.roleType),
          highestRoleLevel: await RBACService.getUserHighestRoleLevel(user._id),
          ipAddress
        };

        next();
      } catch (error) {
        logger.warn('Failed to populate RBAC context:', error);
        // Don't fail the request, just continue without context
        next();
      }
    };
  }

  /**
   * Create a combined middleware that checks both permissions and roles
   */
  static requireAccess(config: {
    permissions?: string | string[];
    roles?: RoleType | RoleType[];
    legacyRoles?: LegacyRole | LegacyRole[];
    minimumLevel?: RoleLevel;
    requireAll?: boolean; // For permissions only
  }) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          throw new CustomError('UNAUTHORIZED', 'Authentication required');
        }

        let hasAccess = false;
        const ipAddress = req.ip || req.connection.remoteAddress || '';

        // Check permissions
        if (config.permissions) {
          const permissionList = Array.isArray(config.permissions) ? config.permissions : [config.permissions];
          const requireAll = config.requireAll || false;

          const context = {
            resourceId: req.params.id || req.params.userId,
            ipAddress
          };

          if (requireAll) {
            hasAccess = await RBACService.hasAllPermissions(user._id, permissionList, context);
          } else {
            hasAccess = await RBACService.hasAnyPermission(user._id, permissionList, context);
          }
        }

        // Check new RBAC roles
        if (!hasAccess && config.roles) {
          const roleList = Array.isArray(config.roles) ? config.roles : [config.roles];
          hasAccess = await RBACService.hasRole(user._id, roleList);
        }

        // Check legacy roles
        if (!hasAccess && config.legacyRoles) {
          const legacyRoleList = Array.isArray(config.legacyRoles) ? config.legacyRoles : [config.legacyRoles];
          for (const legacyRole of legacyRoleList) {
            if (await RBACService.hasLegacyRole(user._id, legacyRole)) {
              hasAccess = true;
              break;
            }
          }
        }

        // Check minimum role level
        if (!hasAccess && config.minimumLevel !== undefined) {
          hasAccess = await RBACService.hasMinimumRoleLevel(user._id, config.minimumLevel);
        }

        if (!hasAccess) {
          logger.warn(`Access denied for user ${user._id}`, {
            userId: user._id,
            config,
            ipAddress
          });
          throw new CustomError('FORBIDDEN', 'Insufficient privileges');
        }

        // Populate RBAC context
        if (!req.rbacContext) {
          req.rbacContext = {
            permissions: await RBACService.getUserPermissions(user._id),
            roles: (await RBACService.getUserActiveRoles(user._id)).map(ur => ur.roleType),
            highestRoleLevel: await RBACService.getUserHighestRoleLevel(user._id),
            ipAddress
          };
        }

        next();
      } catch (error) {
        logger.error('Combined access check error:', error);
        res.status(error instanceof CustomError ? 403 : 500).json({
          success: false,
          message: error instanceof Error ? error.message : 'Access denied'
        });
      }
    };
  }
}

// Export individual functions for backward compatibility
export const requireRole = RBACMiddleware.requireRole;
export const requirePermission = RBACMiddleware.requirePermission;
export const requireNewRole = RBACMiddleware.requireNewRole;
export const requireMinimumLevel = RBACMiddleware.requireMinimumLevel;
export const requireOwnership = RBACMiddleware.requireOwnership;
export const populateRBACContext = RBACMiddleware.populateRBACContext;
export const requireAccess = RBACMiddleware.requireAccess;
