import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';
import { config } from '../config/config';
import { TokenPayload } from '../types/auth.types';
import { IUser, User } from '../models/User';
import { RoleType } from '../models/Role';
import { logger } from '../utils/logger';
import { securityEventService } from '../services/securityEvent.service';
import { SecurityEventType, SecurityEventStatus } from '../types/security-events.types';
import { UAParser } from 'ua-parser-js';
import { AuthService } from '../services/auth.service';
import { getClientInfo } from '../utils/controllerUtils';

declare global {
  namespace Express {
    interface Request {
      token?: string;
    }
  }
}

export const protect = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response | void> => {
  try {
    const token = extractToken(req);

    if (!token) {
      // Log authentication attempt without token
      try {
        await securityEventService.logLoginEvent(
          undefined, // No user ID available
          SecurityEventStatus.FAILURE,
          req,
          {
            loginMethod: 'password', // Default method
            failureReason: 'No authentication token provided',
            attemptNumber: 1
          }
        );
      } catch (loggingError) {
        logger.error('Failed to log authentication attempt:', loggingError);
      }

      return res.status(401).json({
        status: 'error',
        message: 'Authentication required'
      });
    }

    // Verify token
    const decoded = (jwt as any).verify(token, config.JWT_SECRET) as TokenPayload;

    // Find user and check if session exists
    const user = await User.findById(decoded.userId).select('-password');
    if (!user) {
      logger.error(`User not found for ID: ${decoded.userId}`);

      // Log authentication failure for non-existent user
      try {
        await securityEventService.logLoginEvent(
          decoded.userId,
          SecurityEventStatus.FAILURE,
          req,
          {
            loginMethod: 'password',
            failureReason: 'User account no longer exists',
            attemptNumber: 1
          }
        );
      } catch (loggingError) {
        logger.error('Failed to log authentication failure:', loggingError);
      }

      return res.status(401).json({
        status: 'error',
        message: 'User no longer exists'
      });
    }

    if (!decoded.userId) {
      logger.warn('Authentication failed: Invalid token payload');

      // Log authentication failure for invalid token payload
      try {
        await securityEventService.logLoginEvent(
          undefined,
          SecurityEventStatus.FAILURE,
          req,
          {
            loginMethod: 'password',
            failureReason: 'Invalid token payload - missing userId',
            attemptNumber: 1
          }
        );
      } catch (loggingError) {
        logger.error('Failed to log authentication failure:', loggingError);
      }

      return res.status(401).json({
        status: 'error',
        message: 'Invalid authentication token'
      });
    }

    // Check for admin role in headers or cookies
    const adminRoleHeader = req.header('X-User-Role');
    const adminCookie = req.cookies['X-User-Role'];
    const isAdminHeader = req.header('X-User-Is-Admin');
    const isAdminCookie = req.cookies['X-User-Is-Admin'];

    // If admin role is indicated in headers or cookies, ensure it's set in the user object
    if (
      (adminRoleHeader === 'admin' || adminCookie === 'admin') || // Keep legacy string for header compatibility
      (isAdminHeader === 'true' || isAdminCookie === 'true')
    ) {
      // Only set admin role if the user actually has it in the database
      if (user.role === RoleType.ADMIN_USER) {
        // logger.info(`Admin access granted for user ${user._id}`);
      } else {
        // logger.warn(`Admin role requested but not authorized for user ${user._id}`);
      }
    }

    // Ensure the user object has the role property from the database
    if (!user.role && (user as any)._doc && (user as any)._doc.role) {
      user.role = (user as any)._doc.role;
    }

    // Update session activity and potentially refresh token
    try {
      const clientInfo = await getClientInfo(req);
      const activityResult = await AuthService.updateSessionActivity(
        decoded.userId,
        token,
        clientInfo
      );

      // If token was refreshed, set new cookies
      if (activityResult.refreshed && activityResult.tokens) {
        const accessExpMs = parseDuration(config.JWT_ACCESS_EXPIRATION);
        const refreshExpMs = parseDuration(config.JWT_REFRESH_EXPIRATION);

        res.cookie("accesstoken", activityResult.tokens.accessToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
          path: "/",
          maxAge: accessExpMs,
        });

        res.cookie("refreshtoken", activityResult.tokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
          path: "/",
          maxAge: refreshExpMs,
        });

        // Update the token in the request for this request
        req.token = activityResult.tokens.accessToken;

        logger.info(`Token refreshed automatically due to activity for user ${decoded.userId}`);
      } else {
        req.token = token;
      }
    } catch (activityError) {
      // Don't fail the request if activity update fails, just log and continue
      logger.warn('Failed to update session activity:', activityError);
      req.token = token;
    }

    req.user = user as any;

    // Log successful authentication
    try {
      await securityEventService.logLoginEvent(
        user._id.toString(),
        SecurityEventStatus.SUCCESS,
        req,
        {
          loginMethod: 'password', // JWT token-based authentication
          sessionId: (decoded as any).sessionId || token.substring(0, 16), // Use sessionId if available, or token fragment
          profileId: user._id.toString()
        }
      );
    } catch (loggingError) {
      logger.error('Failed to log successful authentication:', loggingError);
      // Don't block the request for logging failures
    }
    next();
  } catch (error: unknown) {
    if (error instanceof Error && error.name === 'JsonWebTokenError') {
      logger.warn('Invalid token:', error.message);

      // Log JWT verification failure
      try {
        await securityEventService.logLoginEvent(
          undefined,
          SecurityEventStatus.FAILURE,
          req,
          {
            loginMethod: 'password',
            failureReason: `JWT verification failed: ${error.message}`,
            attemptNumber: 1
          }
        );
      } catch (loggingError) {
        logger.error('Failed to log JWT verification failure:', loggingError);
      }

      return res.status(401).json({
        status: 'error',
        message: 'Invalid authentication token'
      });
    }

    if (error instanceof Error && error.name === 'TokenExpiredError') {
      logger.warn('Token expired:', error.message);
      return res.status(401).json({
        status: 'error',
        message: 'Token expired - please refresh your token or log in again'
      });
    }

    logger.error('Authentication error:', error instanceof Error ? error.message : 'Unknown error');

    // Log general authentication error
    try {
      await securityEventService.logLoginEvent(
        undefined,
        SecurityEventStatus.FAILURE,
        req,
        {
          loginMethod: 'password',
          failureReason: `Authentication error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          attemptNumber: 1
        }
      );
    } catch (loggingError) {
      logger.error('Failed to log authentication error:', loggingError);
    }
    return res.status(500).json({
      status: 'error',
      message: 'Internal server error during authentication'
    });
  }
};

const extractToken = (req: Request): string | null => {
  // Check cookies first
  const token = req.cookies.accessToken || req.cookies.accesstoken ||
                // Then check Authorization header
                req.header('Authorization')?.replace('Bearer ', '') ||
                // Finally check query parameter
                req.query.token as string;

  return token || null;
};

/**
 * Extract device information from request for security logging
 */
const extractDeviceInfo = (req: Request) => {
  try {
    const userAgent = req.headers['user-agent'] || '';
    const parser = new UAParser(userAgent);
    const result = parser.getResult();

    return {
      userAgent,
      browser: result.browser.name,
      browserVersion: result.browser.version,
      os: result.os.name,
      osVersion: result.os.version,
      device: result.device.type || 'desktop',
      ip: (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
          req.socket.remoteAddress ||
          'unknown'
    };
  } catch (error) {
    logger.error('Error extracting device info:', error);
    return {
      userAgent: req.headers['user-agent'] || '',
      browser: 'unknown',
      browserVersion: 'unknown',
      os: 'unknown',
      osVersion: 'unknown',
      device: 'unknown',
      ip: req.socket.remoteAddress || 'unknown'
    };
  }
};

/**
 * Parse duration string (like "1h", "30d") to milliseconds
 */
const parseDuration = (duration: string): number => {
  const units: { [key: string]: number } = {
    s: 1000,
    m: 60 * 1000,
    h: 60 * 60 * 1000,
    d: 24 * 60 * 60 * 1000
  };

  const match = duration.match(/^(\d+)([smhd])$/);
  if (!match) {
    throw new Error(`Invalid duration format: ${duration}`);
  }

  const [, value, unit] = match;
  return parseInt(value) * units[unit];
};

export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractToken(req);

    if (!token) {
      return next();
    }

    const decoded = (jwt as any).verify(token, config.JWT_SECRET) as TokenPayload;
    const user = await User.findById(decoded.userId);

    if (user) {
      // Update session activity for optional auth as well
      try {
        const clientInfo = await getClientInfo(req);
        const activityResult = await AuthService.updateSessionActivity(
          decoded.userId,
          token,
          clientInfo
        );

        // If token was refreshed, set new cookies
        if (activityResult.refreshed && activityResult.tokens) {
          const accessExpMs = parseDuration(config.JWT_ACCESS_EXPIRATION);
          const refreshExpMs = parseDuration(config.JWT_REFRESH_EXPIRATION);

          res.cookie("accesstoken", activityResult.tokens.accessToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
            path: "/",
            maxAge: accessExpMs,
          });

          res.cookie("refreshtoken", activityResult.tokens.refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
            path: "/",
            maxAge: refreshExpMs,
          });

          req.token = activityResult.tokens.accessToken;
        } else {
          req.token = token;
        }
      } catch (activityError) {
        // Don't fail optional auth if activity update fails
        req.token = token;
      }

      req.user = user as any;
    }

    next();
  } catch (error) {
    // If token is invalid, just continue without setting user
    next();
  }
};

export const requireRoles = (...roles: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user: any = req.user;
      if (!user) {
        // Log authentication required for role-based access
        try {
          await securityEventService.logLoginEvent(
            undefined,
            SecurityEventStatus.FAILURE,
            req,
            {
              loginMethod: 'password',
              failureReason: `Role-based access attempt without authentication. Required roles: ${roles.join(', ')}`,
              attemptNumber: 1
            }
          );
        } catch (loggingError) {
          logger.error('Failed to log role-based auth failure:', loggingError);
        }

        return res.status(401).json({ message: 'Not authenticated' });
      }

      if (!roles.includes(user.role)) {
        // Log authorization failure
        try {
          await securityEventService.logLoginEvent(
            user._id?.toString(),
            SecurityEventStatus.FAILURE,
            req,
            {
              loginMethod: 'password',
              failureReason: `Insufficient privileges. User role: ${user.role}, Required roles: ${roles.join(', ')}`,
              attemptNumber: 1
            }
          );
        } catch (loggingError) {
          logger.error('Failed to log authorization failure:', loggingError);
        }

        return res.status(403).json({ message: 'Not authorized' });
      }

      // Log successful role-based access
      try {
        await securityEventService.logLoginEvent(
          user._id?.toString(),
          SecurityEventStatus.SUCCESS,
          req,
          {
            loginMethod: 'password',
            sessionId: req.token?.substring(0, 16) || 'unknown',
            profileId: user._id?.toString()
          }
        );
      } catch (loggingError) {
        logger.error('Failed to log successful role-based access:', loggingError);
      }

      next();
    } catch (error) {
      logger.error('Error in requireRoles middleware:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  };
};
