import { Request, Response, NextFunction, Router } from 'express';
import { Role, RoleType, RoleLevel } from '../models/Role';
import { Permission, PermissionCategory } from '../models/Permission';
import { RBACService } from '../services/rbac.service';
import { logger } from '../utils/logger';
import { CustomError } from '../utils/errors';

// Extend Express Request interface to include RBAC context
declare global {
  namespace Express {
    interface Request {
      rbacContext?: {
        permissions: string[];
        roles: RoleType[];
        highestRoleLevel: RoleLevel;
        ipAddress: string;
      };
    }
  }
}

/**
 * Dynamic RBAC Route Manager with Performance Optimizations
 *
 * This middleware automatically protects routes based on database-stored permissions and roles.
 * It includes Redis caching, request deduplication, and batch loading for production performance.
 */

interface RouteProtectionConfig {
  path: string;
  method: string;
  requiredPermissions?: string[];
  requiredRoles?: RoleType[];
  minimumLevel?: RoleLevel;
  requireAll?: boolean;
  resourceIdParam?: string;
  customConditions?: (req: Request) => Promise<boolean>;
}

interface DynamicRBACConfig {
  autoProtectAdmin?: boolean;
  defaultUserPermissions?: string[];
  routeOverrides?: RouteProtectionConfig[];
  cacheTimeout?: number;
  enableAuditLogging?: boolean;
  enableRedisCache?: boolean;
  batchSize?: number;
  enableRequestDeduplication?: boolean;
}

interface CacheKey {
  permissions: string;
  roles: string;
  routes: string;
  userPermissions: (userId: string) => string;
  userRoles: (userId: string) => string;
}

class DynamicRBACManager {
  private static instance: DynamicRBACManager;
  private routeConfigs: Map<string, RouteProtectionConfig> = new Map();
  private permissionCache: Map<string, any> = new Map();
  private roleCache: Map<string, any> = new Map();
  private userPermissionCache: Map<string, { permissions: string[], roles: RoleType[], timestamp: number }> = new Map();
  private lastCacheUpdate: number = 0;
  private config: DynamicRBACConfig;

  // Request deduplication for identical permission checks
  private pendingChecks: Map<string, Promise<boolean>> = new Map();

  // Redis client (optional)
  private redisClient: any = null;

  // Cache keys
  private cacheKeys: CacheKey = {
    permissions: 'rbac:permissions',
    roles: 'rbac:roles',
    routes: 'rbac:routes',
    userPermissions: (userId: string) => `rbac:user:${userId}:permissions`,
    userRoles: (userId: string) => `rbac:user:${userId}:roles`
  };

  constructor(config: DynamicRBACConfig = {}) {
    this.config = {
      autoProtectAdmin: true,
      defaultUserPermissions: ['user.read.own', 'user.update.own', 'profile.create', 'profile.read.own', 'profile.update.own'],
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
      enableAuditLogging: true,
      enableRedisCache: false, // Enable in production with Redis
      batchSize: 100,
      enableRequestDeduplication: true,
      ...config
    };

    // Initialize Redis if enabled
    if (this.config.enableRedisCache) {
      this.initializeRedis();
    }
  }

  public static getInstance(config?: DynamicRBACConfig): DynamicRBACManager {
    if (!DynamicRBACManager.instance) {
      DynamicRBACManager.instance = new DynamicRBACManager(config);
    }
    return DynamicRBACManager.instance;
  }

  private async initializeRedis(): Promise<void> {
    try {
      const Redis = require('ioredis');
      this.redisClient = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      await this.redisClient.connect();
      logger.info('Redis connected for RBAC caching');
    } catch (error) {
      logger.warn('Redis connection failed, falling back to memory cache:', error);
      this.redisClient = null;
      this.config.enableRedisCache = false;
    }
  }

  /**
   * Load RBAC configuration with performance optimizations
   */
  async loadRBACConfiguration(): Promise<void> {
    try {
      const now = Date.now();

      // Check Redis cache first
      if (this.redisClient && this.config.enableRedisCache) {
        const cachedData = await this.loadFromRedisCache();
        if (cachedData) {
          this.applyCache(cachedData);
          return;
        }
      }

      // Check memory cache
      if (this.lastCacheUpdate && (now - this.lastCacheUpdate) < this.config.cacheTimeout!) {
        return; // Use cached data
      }

      logger.info('Loading dynamic RBAC configuration from database...');

      // Batch load with optimized queries
      const [permissions, roles] = await Promise.all([
        Permission.find({ isActive: true })
          .select('name displayName resource action conditions metadata category')
          .lean()
          .limit(this.config.batchSize! * 10), // Allow larger batch for permissions
        Role.find({ isActive: true })
          .select('name displayName level permissions isActive')
          .lean()
          .limit(this.config.batchSize!)
      ]);

      // Clear existing caches
      this.permissionCache.clear();
      this.roleCache.clear();
      this.routeConfigs.clear();
      this.userPermissionCache.clear(); // Clear user cache on config reload

      // Cache permissions with optimized indexing
      const permissionIndex = new Map();
      permissions.forEach(permission => {
        const key = `${permission.resource}.${permission.action}`;
        this.permissionCache.set(key, permission);
        this.permissionCache.set(permission.name, permission);

        // Build reverse index for faster lookups
        if (!permissionIndex.has(permission.resource)) {
          permissionIndex.set(permission.resource, []);
        }
        permissionIndex.get(permission.resource).push(permission);
      });

      // Cache roles with permission lookup optimization
      roles.forEach(role => {
        this.roleCache.set(role.name, role);
      });

      // Generate route protection configurations
      this.generateRouteConfigurations(permissions);

      // Save to Redis if enabled
      if (this.redisClient && this.config.enableRedisCache) {
        await this.saveToRedisCache({ permissions, roles, routeConfigs: Array.from(this.routeConfigs.entries()) });
      }

      this.lastCacheUpdate = now;
      logger.info(`Loaded ${permissions.length} permissions and ${roles.length} roles for dynamic RBAC`);

    } catch (error) {
      logger.error('Failed to load RBAC configuration:', error);
      throw new Error('RBAC configuration load failed');
    }
  }

  private async loadFromRedisCache(): Promise<any> {
    try {
      const pipeline = this.redisClient.pipeline();
      pipeline.get(this.cacheKeys.permissions);
      pipeline.get(this.cacheKeys.roles);
      pipeline.get(this.cacheKeys.routes);

      const results = await pipeline.exec();

      if (results && results.every(([err, data]: [Error | null, any]) => !err && data)) {
        return {
          permissions: JSON.parse(results[0][1]),
          roles: JSON.parse(results[1][1]),
          routeConfigs: JSON.parse(results[2][1])
        };
      }
    } catch (error) {
      logger.warn('Failed to load from Redis cache:', error);
    }
    return null;
  }

  private async saveToRedisCache(data: any): Promise<void> {
    try {
      const pipeline = this.redisClient.pipeline();
      const ttl = Math.floor(this.config.cacheTimeout! / 1000);

      pipeline.setex(this.cacheKeys.permissions, ttl, JSON.stringify(data.permissions));
      pipeline.setex(this.cacheKeys.roles, ttl, JSON.stringify(data.roles));
      pipeline.setex(this.cacheKeys.routes, ttl, JSON.stringify(data.routeConfigs));

      await pipeline.exec();
    } catch (error) {
      logger.warn('Failed to save to Redis cache:', error);
    }
  }

  private applyCache(cachedData: any): void {
    // Apply permissions cache
    this.permissionCache.clear();
    cachedData.permissions.forEach((permission: any) => {
      const key = `${permission.resource}.${permission.action}`;
      this.permissionCache.set(key, permission);
      this.permissionCache.set(permission.name, permission);
    });

    // Apply roles cache
    this.roleCache.clear();
    cachedData.roles.forEach((role: any) => {
      this.roleCache.set(role.name, role);
    });

    // Apply route configs cache
    this.routeConfigs.clear();
    cachedData.routeConfigs.forEach(([key, config]: [string, RouteProtectionConfig]) => {
      this.routeConfigs.set(key, config);
    });

    this.lastCacheUpdate = Date.now();
    logger.info('Applied RBAC configuration from cache');
  }

  /**
   * Generate route protection configurations based on database permissions
   */
  private generateRouteConfigurations(permissions: any[]): void {
    // Auto-generate route patterns based on resource.action permissions
    permissions.forEach(permission => {
      const { resource, action, name, category, metadata } = permission;

      // Map database permissions to route patterns
      const routePatterns = this.mapPermissionToRoutes(resource, action, category);

      routePatterns.forEach(pattern => {
        const config: RouteProtectionConfig = {
          path: pattern.path,
          method: pattern.method,
          requiredPermissions: [name],
          minimumLevel: this.getMinimumLevelForPermission(metadata),
          resourceIdParam: pattern.resourceParam
        };

        const routeKey = `${pattern.method}:${pattern.path}`;
        this.routeConfigs.set(routeKey, config);
      });
    });

    // Add special admin routes protection
    if (this.config.autoProtectAdmin) {
      this.addAdminRouteProtection();
    }

    // Apply custom route overrides
    if (this.config.routeOverrides) {
      this.config.routeOverrides.forEach(override => {
        const routeKey = `${override.method}:${override.path}`;
        this.routeConfigs.set(routeKey, override);
      });
    }
  }

  /**
   * Map database permissions to actual route patterns
   */
  private mapPermissionToRoutes(resource: string, action: string, category: string): Array<{
    path: string;
    method: string;
    resourceParam?: string;
  }> {
    const routes: Array<{ path: string; method: string; resourceParam?: string; }> = [];

    // Base route patterns for each resource - ENHANCED with new resources
    const basePatterns: Record<string, string> = {
      user: '/api/users',
      profile: '/api/profiles',
      admin: '/api/admin',
      analytics: '/api/analytics',
      system: '/api/admin/system',
      content: '/api/content',
      merchant: '/api/merchant',
      payment: '/api/payments',
      loyalty: '/api/loyalty',
      inventory: '/api/inventory',
      vault: '/api/vault',
      notification: '/api/notifications',
      security: '/api/security',
      finance: '/api/finance',
      logs: '/api/logs',
      fraud: '/api/fraud',
      // Add new resources as needed
      blog: '/api/blog',
      article: '/api/articles',
      comment: '/api/comments',
      order: '/api/orders',
      product: '/api/products',
      billing: '/api/billing',
      transaction: '/api/transactions',
      email: '/api/emails',
      report: '/api/reports',
      config: '/api/config',
      backup: '/api/backup',
      audit: '/api/audit'
    };

    const basePath = basePatterns[resource] || `/api/${resource}`;

    // Map actions to HTTP methods and specific paths
    switch (action) {
      case 'create':
        routes.push({ path: basePath, method: 'POST' });
        break;

      case 'read':
        routes.push({ path: basePath, method: 'GET' });
        routes.push({ path: `${basePath}/:id`, method: 'GET', resourceParam: 'id' });
        break;

      case 'update':
        routes.push({ path: `${basePath}/:id`, method: 'PUT', resourceParam: 'id' });
        routes.push({ path: `${basePath}/:id`, method: 'PATCH', resourceParam: 'id' });
        break;

      case 'delete':
        routes.push({ path: `${basePath}/:id`, method: 'DELETE', resourceParam: 'id' });
        break;

      case 'manage':
        routes.push({ path: basePath, method: 'GET' });
        routes.push({ path: basePath, method: 'POST' });
        routes.push({ path: `${basePath}/:id`, method: 'PUT', resourceParam: 'id' });
        routes.push({ path: `${basePath}/:id`, method: 'DELETE', resourceParam: 'id' });
        break;

      case 'ban':
        routes.push({ path: `${basePath}/:id/ban`, method: 'POST', resourceParam: 'id' });
        routes.push({ path: `${basePath}/:id/unban`, method: 'POST', resourceParam: 'id' });
        break;

      case 'role_manage':
        routes.push({ path: `${basePath}/:id/roles`, method: 'POST', resourceParam: 'id' });
        routes.push({ path: `${basePath}/:id/roles/:roleType`, method: 'DELETE', resourceParam: 'id' });
        break;

      case 'moderate':
        routes.push({ path: `${basePath}/:id/moderate`, method: 'POST', resourceParam: 'id' });
        routes.push({ path: `${basePath}/:id/approve`, method: 'POST', resourceParam: 'id' });
        routes.push({ path: `${basePath}/:id/reject`, method: 'POST', resourceParam: 'id' });
        break;

      case 'audit':
        routes.push({ path: `${basePath}/audit`, method: 'GET' });
        routes.push({ path: `${basePath}/reports`, method: 'GET' });
        break;

      case 'configure':
        routes.push({ path: `${basePath}/config`, method: 'GET' });
        routes.push({ path: `${basePath}/config`, method: 'PUT' });
        break;
    }

    return routes;
  }

  /**
   * Determine minimum role level required for a permission based on metadata
   */
  private getMinimumLevelForPermission(metadata: any): RoleLevel | undefined {
    if (!metadata) return undefined;

    switch (metadata.riskLevel) {
      case 'critical':
        return RoleLevel.SUPER_ADMIN;
      case 'high':
        return RoleLevel.MAJOR_ADMIN;
      case 'medium':
        return RoleLevel.ADMIN_USER;
      default:
        return undefined;
    }
  }

  /**
   * Add protection for admin routes
   */
  private addAdminRouteProtection(): void {
    const adminRoutes = [
      { path: '/api/admin/:path*', method: 'GET', minimumLevel: RoleLevel.PROXY_ADMIN },
      { path: '/api/admin/:path*', method: 'POST', minimumLevel: RoleLevel.ADMIN_USER },
      { path: '/api/admin/:path*', method: 'PUT', minimumLevel: RoleLevel.ADMIN_USER },
      { path: '/api/admin/:path*', method: 'DELETE', minimumLevel: RoleLevel.MAJOR_ADMIN }
    ];

    adminRoutes.forEach(route => {
      const routeKey = `${route.method}:${route.path}`;
      this.routeConfigs.set(routeKey, {
        path: route.path,
        method: route.method,
        minimumLevel: route.minimumLevel
      });
    });
  }

  /**
   * Get route protection configuration for a specific route
   */
  getRouteProtection(method: string, path: string): RouteProtectionConfig | null {
    // Try exact match first
    const exactKey = `${method}:${path}`;
    if (this.routeConfigs.has(exactKey)) {
      return this.routeConfigs.get(exactKey)!;
    }

    // Try pattern matching for parameterized routes
    for (const [routeKey, config] of this.routeConfigs.entries()) {
      const [routeMethod, routePath] = routeKey.split(':');
      if (routeMethod === method && this.pathMatches(path, routePath)) {
        return config;
      }
    }

    return null;
  }

  /**
   * Check if a request path matches a route pattern
   */
  private pathMatches(requestPath: string, routePattern: string): boolean {
    // Convert route pattern to regex
    const regexPattern = routePattern
      .replace(/:[^/]+/g, '[^/]+')  // Replace :param with regex
      .replace(/\*/g, '.*');        // Replace * with regex

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(requestPath);
  }

  /**
   * Enhanced user permission checking with caching and deduplication
   */
  private async getUserPermissionsWithCache(userId: string): Promise<{ permissions: string[], roles: RoleType[] }> {
    // Check memory cache first
    const cached = this.userPermissionCache.get(userId);
    if (cached && (Date.now() - cached.timestamp) < 30000) { // 30 seconds cache
      return { permissions: cached.permissions, roles: cached.roles };
    }

    // Check Redis cache
    if (this.redisClient && this.config.enableRedisCache) {
      try {
        const [permissionsStr, rolesStr] = await Promise.all([
          this.redisClient.get(this.cacheKeys.userPermissions(userId)),
          this.redisClient.get(this.cacheKeys.userRoles(userId))
        ]);

        if (permissionsStr && rolesStr) {
          const permissions = JSON.parse(permissionsStr);
          const roles = JSON.parse(rolesStr);

          // Update memory cache
          this.userPermissionCache.set(userId, {
            permissions,
            roles,
            timestamp: Date.now()
          });

          return { permissions, roles };
        }
      } catch (error) {
        logger.warn('Failed to load user permissions from Redis:', error);
      }
    }

    // Load from database
    const [permissions, userRoles] = await Promise.all([
      RBACService.getUserPermissions(userId),
      RBACService.getUserActiveRoles(userId)
    ]);

    const roles = userRoles.map(ur => ur.roleType);

    // Cache in memory
    this.userPermissionCache.set(userId, {
      permissions,
      roles,
      timestamp: Date.now()
    });

    // Cache in Redis
    if (this.redisClient && this.config.enableRedisCache) {
      try {
        const pipeline = this.redisClient.pipeline();
        pipeline.setex(this.cacheKeys.userPermissions(userId), 300, JSON.stringify(permissions)); // 5 minutes
        pipeline.setex(this.cacheKeys.userRoles(userId), 300, JSON.stringify(roles));
        await pipeline.exec();
      } catch (error) {
        logger.warn('Failed to cache user permissions in Redis:', error);
      }
    }

    return { permissions, roles };
  }

  /**
   * Create middleware function for dynamic RBAC protection with performance optimizations
   */
  createMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = req.user as any;
        if (!user) {
          return res.status(401).json({
            success: false,
            message: 'Authentication required'
          });
        }

        // COMPLETE ADMIN BYPASS - Skip all RBAC checks for admin users
        const isLegacyAdmin = user.role === RoleType.ADMIN_USER || user.role === RoleType.SUPER_ADMIN;
        if (isLegacyAdmin) {
          return next(); // Admin users bypass all RBAC restrictions
        }

        // Check RBAC admin roles as well
        try {
          const userLevel = await RBACService.getUserHighestRoleLevel(user._id);
          const isRBACAdmin = userLevel >= RoleLevel.PROXY_ADMIN; // Level 30 and above
          if (isRBACAdmin) {
            return next(); // RBAC admin users bypass all restrictions
          }
        } catch (error) {
          // If RBAC check fails, continue with legacy admin check
          if (isLegacyAdmin) {
            return next();
          }
        }

        // Ensure RBAC configuration is loaded
        await this.loadRBACConfiguration();

        const method = req.method;
        const path = req.route?.path || req.path;

        // Get protection configuration for this route
        const protection = this.getRouteProtection(method, path);

        if (!protection) {
          // No specific protection configured, apply default user permissions check
          if (this.config.defaultUserPermissions && this.config.defaultUserPermissions.length > 0) {
            const { permissions, roles } = await this.getUserPermissionsWithCache(user._id);

            // Check if user has default permissions OR is an admin with elevated permissions
            const hasDefaultPermission = this.config.defaultUserPermissions.some(p => permissions.includes(p));

            // If user doesn't have default permissions, check if they have admin-level access
            if (!hasDefaultPermission) {
              // Check if user has admin role level (>= PROXY_ADMIN = 30)
              const userLevel = await RBACService.getUserHighestRoleLevel(user._id);
              const isAdmin = userLevel >= RoleLevel.PROXY_ADMIN;

              // Check if user has any admin permissions
              const adminPermissions = ['admin.manage', 'user.manage', 'user.read.all', 'user.update.all'];
              const hasAdminPermission = adminPermissions.some(p => permissions.includes(p));

              // Allow access if user is admin OR has admin permissions
              if (!isAdmin && !hasAdminPermission) {
                return res.status(403).json({
                  success: false,
                  message: 'Insufficient permissions for this action'
                });
              }
            }
          }

          return next(); // No protection needed or permissions satisfied
        }

        // Apply the configured protection with deduplication
        const hasAccess = await this.checkAccessWithDeduplication(user._id, protection, req);

        if (!hasAccess) {
          if (this.config.enableAuditLogging) {
            logger.warn(`Dynamic RBAC: Access denied for user ${user._id}`, {
              userId: user._id,
              method,
              path,
              protection,
              userRole: user.role || ((user as any)._doc ? (user as any)._doc.role : null),
              timestamp: new Date().toISOString()
            });
          }

          return res.status(403).json({
            success: false,
            message: 'Insufficient permissions to access this resource'
          });
        }

        // Access granted - add RBAC context to request
        const { permissions, roles } = await this.getUserPermissionsWithCache(user._id);
        req.rbacContext = {
          permissions,
          roles,
          highestRoleLevel: await RBACService.getUserHighestRoleLevel(user._id),
          ipAddress: req.ip || req.connection.remoteAddress || ''
        };

        next();

      } catch (error) {
        logger.error('Dynamic RBAC middleware error:', error);
        return res.status(500).json({
          success: false,
          message: 'Internal server error during authorization'
        });
      }
    };
  }

  /**
   * Check access with request deduplication to prevent duplicate database calls
   */
  private async checkAccessWithDeduplication(userId: string, protection: RouteProtectionConfig, req: Request): Promise<boolean> {
    if (!this.config.enableRequestDeduplication) {
      return this.checkAccess(userId, protection, req);
    }

    // Create deduplication key
    const dedupKey = `${userId}:${JSON.stringify(protection)}:${req.params?.id || ''}`;

    // Check if same request is already pending
    if (this.pendingChecks.has(dedupKey)) {
      return this.pendingChecks.get(dedupKey)!;
    }

    // Start new check and cache the promise
    const checkPromise = this.checkAccess(userId, protection, req);
    this.pendingChecks.set(dedupKey, checkPromise);

    try {
      const result = await checkPromise;
      // Clean up after short delay to allow immediate reuse
      setTimeout(() => this.pendingChecks.delete(dedupKey), 100);
      return result;
    } catch (error) {
      this.pendingChecks.delete(dedupKey);
      throw error;
    }
  }

  /**
   * Check if user has access based on protection configuration
   */
  private async checkAccess(userId: string, protection: RouteProtectionConfig, req: Request): Promise<boolean> {
    // Check permissions
    if (protection.requiredPermissions && protection.requiredPermissions.length > 0) {
      const context = {
        resourceId: protection.resourceIdParam ? req.params[protection.resourceIdParam] : undefined,
        ipAddress: req.ip || req.connection.remoteAddress || ''
      };

      const hasPermissions = protection.requireAll
        ? await RBACService.hasAllPermissions(userId, protection.requiredPermissions, context)
        : await RBACService.hasAnyPermission(userId, protection.requiredPermissions, context);

      if (!hasPermissions) return false;
    }

    // Check roles
    if (protection.requiredRoles && protection.requiredRoles.length > 0) {
      const hasRole = await RBACService.hasRole(userId, protection.requiredRoles);
      if (!hasRole) return false;
    }

    // Check minimum level
    if (protection.minimumLevel !== undefined) {
      const hasLevel = await RBACService.hasMinimumRoleLevel(userId, protection.minimumLevel);
      if (!hasLevel) return false;
    }

    // Check custom conditions
    if (protection.customConditions) {
      const customResult = await protection.customConditions(req);
      if (!customResult) return false;
    }

    return true;
  }

  /**
   * Get current RBAC configuration summary with performance metrics
   */
  async getConfigurationSummary(): Promise<{
    totalPermissions: number;
    totalRoles: number;
    protectedRoutes: number;
    lastUpdate: Date;
    cacheHitRate?: number;
    performanceMetrics?: any;
  }> {
    await this.loadRBACConfiguration();

    return {
      totalPermissions: this.permissionCache.size,
      totalRoles: this.roleCache.size,
      protectedRoutes: this.routeConfigs.size,
      lastUpdate: new Date(this.lastCacheUpdate),
      cacheHitRate: this.calculateCacheHitRate(),
      performanceMetrics: {
        userCacheSize: this.userPermissionCache.size,
        pendingChecks: this.pendingChecks.size,
        redisEnabled: !!this.redisClient
      }
    };
  }

  private calculateCacheHitRate(): number {
    // Simplified cache hit rate calculation
    // In production, you'd track hits/misses more precisely
    return this.userPermissionCache.size > 0 ? 0.85 : 0; // Estimated 85% hit rate
  }

  /**
   * Refresh RBAC configuration (force reload from database)
   */
  async refreshConfiguration(): Promise<void> {
    this.lastCacheUpdate = 0; // Force refresh
    this.userPermissionCache.clear(); // Clear user cache
    this.pendingChecks.clear(); // Clear pending checks

    // Clear Redis cache if enabled
    if (this.redisClient && this.config.enableRedisCache) {
      try {
        const pipeline = this.redisClient.pipeline();
        pipeline.del(this.cacheKeys.permissions);
        pipeline.del(this.cacheKeys.roles);
        pipeline.del(this.cacheKeys.routes);
        await pipeline.exec();
      } catch (error) {
        logger.warn('Failed to clear Redis cache:', error);
      }
    }

    await this.loadRBACConfiguration();
    logger.info('Dynamic RBAC configuration refreshed with cache clear');
  }

  /**
   * Clear user-specific cache (useful when user roles change)
   */
  async clearUserCache(userId: string): Promise<void> {
    this.userPermissionCache.delete(userId);

    if (this.redisClient && this.config.enableRedisCache) {
      try {
        const pipeline = this.redisClient.pipeline();
        pipeline.del(this.cacheKeys.userPermissions(userId));
        pipeline.del(this.cacheKeys.userRoles(userId));
        await pipeline.exec();
      } catch (error) {
        logger.warn('Failed to clear user cache in Redis:', error);
      }
    }
  }
}

// Export middleware factory
export const createDynamicRBACMiddleware = (config?: DynamicRBACConfig) => {
  const manager = DynamicRBACManager.getInstance(config);
  return manager.createMiddleware();
};

// Export manager instance for configuration management
export const getDynamicRBACManager = () => {
  return DynamicRBACManager.getInstance();
};

// Export types
export { DynamicRBACConfig, RouteProtectionConfig };
