/**
 * @file bruteForceProtection.ts
 * @description Enhanced Rate Limiting and Brute Force Protection
 * =================================================================
 * 
 * Comprehensive rate limiting system using Redis for distributed
 * rate limiting across multiple server instances.
 * 
 * Features:
 * - Multiple rate limiting strategies
 * - Redis-based distributed storage
 * - Customizable limits per endpoint type
 * - Automatic IP blocking for repeated violations
 * - Detailed logging and monitoring
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
// Note: rate-limit-redis package not available, using memory store as fallback
// import RedisStore from 'rate-limit-redis';
import { getRedisClient } from '../config/redis';
import { logger } from '../utils/logger';

// Create Redis client for rate limiting
const redisClient = getRedisClient();

// Custom key generator that includes user ID if available
const createKeyGenerator = (prefix: string) => {
  return (req: Request): string => {
    const userId = (req as any).user?.id || 'anonymous';
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    return `${prefix}:${userId}:${ip}`;
  };
};

// Enhanced error handler with detailed logging
const createRateLimitHandler = (operation: string, windowMs: number) => {
  return (req: Request, res: Response) => {
    const userId = (req as any).user?.id || 'anonymous';
    const resetTime = new Date(Date.now() + windowMs);
    
    logger.warn(`Rate limit exceeded for ${operation}`, {
      userId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      resetTime
    });

    res.status(429).json({
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: `Too many ${operation} requests. Please try again later.`,
        retryAfter: Math.ceil(windowMs / 1000),
        resetTime
      }
    });
  };
};

// Login rate limiter - strict limits for authentication
export const loginLimiter = rateLimit({
  // store: new RedisStore({
  //   sendCommand: (...args: string[]) => redisClient.call(...args),
  // }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  skipSuccessfulRequests: true, // Don't count successful logins
  keyGenerator: createKeyGenerator('login'),
  handler: createRateLimitHandler('login', 15 * 60 * 1000),
  standardHeaders: true,
  legacyHeaders: false
});

// Badge creation rate limiter - 10 requests per minute
export const badgeCreationLimiter = rateLimit({
  // store: new RedisStore({
  //   sendCommand: (...args: string[]) => redisClient.call(...args),
  // }),
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requests per minute
  keyGenerator: createKeyGenerator('badge-creation'),
  handler: createRateLimitHandler('badge creation', 60 * 1000),
  standardHeaders: true,
  legacyHeaders: false,
  // Custom message for badge creation
  message: {
    error: {
      code: 'BADGE_CREATION_RATE_LIMIT_EXCEEDED',
      message: 'Too many badge creation requests. Limit: 10 per minute.',
    }
  }
});

// Badge update rate limiter - more lenient for updates
export const badgeUpdateLimiter = rateLimit({
  // store: new RedisStore({
  //   sendCommand: (...args: string[]) => redisClient.call(...args),
  // }),
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requests per minute
  keyGenerator: createKeyGenerator('badge-update'),
  handler: createRateLimitHandler('badge update', 60 * 1000),
  standardHeaders: true,
  legacyHeaders: false
});

// Badge progress update limiter - high volume expected
export const badgeProgressLimiter = rateLimit({
  // store: new RedisStore({
  //   sendCommand: (...args: string[]) => redisClient.call(...args),
  // }),
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  keyGenerator: createKeyGenerator('badge-progress'),
  handler: createRateLimitHandler('badge progress update', 60 * 1000),
  standardHeaders: true,
  legacyHeaders: false
});

// General API rate limiter for badge endpoints
export const badgeApiLimiter = rateLimit({
  // store: new RedisStore({
  //   sendCommand: (...args: string[]) => redisClient.call(...args),
  // }),
  windowMs: 60 * 1000, // 1 minute
  max: 200, // 200 requests per minute for general badge API calls
  keyGenerator: createKeyGenerator('badge-api'),
  handler: createRateLimitHandler('badge API', 60 * 1000),
  standardHeaders: true,
  legacyHeaders: false
});

// Sensitive operations limiter - profile changes, admin actions
export const sensitiveOpsLimiter = rateLimit({
  // store: new RedisStore({
  //   sendCommand: (...args: string[]) => redisClient.call(...args),
  // }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 attempts per hour (increased from 10)
  keyGenerator: createKeyGenerator('sensitive-ops'),
  handler: createRateLimitHandler('sensitive operation', 60 * 60 * 1000),
  standardHeaders: true,
  legacyHeaders: false
});

// Aggressive rate limiter for suspected abuse
export const aggressiveLimiter = rateLimit({
  // store: new RedisStore({
  //   sendCommand: (...args: string[]) => redisClient.call(...args),
  // }),
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 50, // 50 requests per 5 minutes
  keyGenerator: createKeyGenerator('aggressive'),
  handler: (req: Request, res: Response) => {
    logger.error('Aggressive rate limit triggered - possible abuse', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      userId: (req as any).user?.id
    });
    
    res.status(429).json({
      error: {
        code: 'ABUSE_DETECTED',
        message: 'Request pattern indicates potential abuse. Access temporarily restricted.',
        retryAfter: 300 // 5 minutes
      }
    });
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Dynamic rate limiter that adjusts based on user tier/privileges
export const createDynamicLimiter = (baseMax: number, windowMs: number = 60 * 1000) => {
  return rateLimit({
    // store: new RedisStore({
    //   sendCommand: (...args: string[]) => redisClient.call(...args),
    // }),
    windowMs,
    max: (req: Request) => {
      const user = (req as any).user;
      if (!user) return Math.floor(baseMax * 0.5); // Reduced for anonymous users
      
      // Adjust limits based on user role/tier
      const { RoleType } = require('../models/Role');
      if (user.role === RoleType.ADMIN_USER) return baseMax * 5;
      if (user.role === 'premium') return baseMax * 2;
      if (user.verified) return baseMax * 1.5;
      
      return baseMax;
    },
    keyGenerator: createKeyGenerator('dynamic'),
    handler: createRateLimitHandler('dynamic', windowMs),
    standardHeaders: true,
    legacyHeaders: false
  });
};

// Middleware to apply appropriate rate limiting based on endpoint
export const smartRateLimiter = (req: Request, res: Response, next: NextFunction) => {
  const path = req.path.toLowerCase();
  const method = req.method.toUpperCase();

  // Badge creation endpoints
  if (method === 'POST' && path.includes('/badges') && !path.includes('/progress')) {
    return badgeCreationLimiter(req, res, next);
  }

  // Badge update endpoints
  if ((method === 'PUT' || method === 'PATCH') && path.includes('/badges')) {
    return badgeUpdateLimiter(req, res, next);
  }

  // Badge progress updates
  if (path.includes('/progress') || path.includes('/activity')) {
    return badgeProgressLimiter(req, res, next);
  }

  // Admin or sensitive operations
  if (path.includes('/admin') || method === 'DELETE') {
    return sensitiveOpsLimiter(req, res, next);
  }

  // Default badge API limiter
  if (path.includes('/badges')) {
    return badgeApiLimiter(req, res, next);
  }

  // No specific limiter needed
  next();
};

// Utility function to check if an IP is currently rate limited
export const checkRateLimit = async (key: string): Promise<{
  limited: boolean;
  remaining: number;
  resetTime: Date;
}> => {
  try {
    const current = await redisClient.get(`rl:${key}`);
    const ttl = await redisClient.ttl(`rl:${key}`);
    
    return {
      limited: current ? parseInt(current) >= 10 : false, // Assuming 10 as default max
      remaining: current ? Math.max(0, 10 - parseInt(current)) : 10,
      resetTime: new Date(Date.now() + (ttl * 1000))
    };
  } catch (error) {
    logger.error('Error checking rate limit:', error);
    return { limited: false, remaining: 10, resetTime: new Date() };
  }
};

export default {
  loginLimiter,
  badgeCreationLimiter,
  badgeUpdateLimiter,
  badgeProgressLimiter,
  badgeApiLimiter,
  sensitiveOpsLimiter,
  aggressiveLimiter,
  smartRateLimiter,
  createDynamicLimiter,
  checkRateLimit
};
