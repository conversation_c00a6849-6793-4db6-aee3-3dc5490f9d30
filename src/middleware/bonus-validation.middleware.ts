import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import { BonusModel, BonusCampaignModel } from '../models/bonus.model';
import { BonusStatus, CampaignStatus } from '../interfaces/bonus.interface';

/**
 * Middleware to validate bonus exists and is accessible
 */
export const validateBonusExists = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const bonusId = req.params.id || req.params.bonusId || req.body.bonusId;
    
    if (!bonusId) {
      return res.status(400).json({
        success: false,
        message: 'Bonus ID is required'
      });
    }

    if (!mongoose.Types.ObjectId.isValid(bonusId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid bonus ID format'
      });
    }

    const bonus = await BonusModel.findById(bonusId);
    if (!bonus) {
      return res.status(404).json({
        success: false,
        message: 'Bonus not found'
      });
    }

    // Attach bonus to request object for use in controllers
    (req as any).bonus = bonus;
    next();
  } catch (error) {
    console.error('Error validating bonus existence:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating bonus'
    });
  }
};

/**
 * Middleware to validate campaign exists and is accessible
 */
export const validateCampaignExists = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const campaignId = req.params.id || req.params.campaignId || req.body.campaignId;
    
    if (!campaignId) {
      return res.status(400).json({
        success: false,
        message: 'Campaign ID is required'
      });
    }

    if (!mongoose.Types.ObjectId.isValid(campaignId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid campaign ID format'
      });
    }

    const campaign = await BonusCampaignModel.findById(campaignId);
    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    // Attach campaign to request object for use in controllers
    (req as any).campaign = campaign;
    next();
  } catch (error) {
    console.error('Error validating campaign existence:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating campaign'
    });
  }
};

/**
 * Middleware to check if user can manage bonuses (admin only)
 */
export const validateBonusManagementPermission = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Check if user has admin role or bonus management permission
    // This would integrate with your RBAC system
    const hasPermission = await checkBonusManagementPermission(user._id);
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to manage bonuses'
      });
    }

    next();
  } catch (error) {
    console.error('Error validating bonus management permission:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating permissions'
    });
  }
};

/**
 * Middleware to validate bonus distribution permissions
 */
export const validateBonusDistributionPermission = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    const bonus = (req as any).bonus;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Check if user can distribute this specific bonus
    const canDistribute = await checkBonusDistributionPermission(user._id, bonus);
    
    if (!canDistribute.allowed) {
      return res.status(403).json({
        success: false,
        message: canDistribute.reason || 'Cannot distribute this bonus'
      });
    }

    next();
  } catch (error) {
    console.error('Error validating bonus distribution permission:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating distribution permissions'
    });
  }
};

/**
 * Middleware to validate bonus is active and within date range
 */
export const validateBonusActive = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const bonus = (req as any).bonus;
    
    if (!bonus) {
      return res.status(400).json({
        success: false,
        message: 'Bonus not found in request context'
      });
    }

    // Check if bonus is active
    if (bonus.status !== BonusStatus.ACTIVE) {
      return res.status(400).json({
        success: false,
        message: `Bonus is ${bonus.status.toLowerCase()}, not active`
      });
    }

    // Check date range
    const now = new Date();
    
    if (bonus.startDate && now < bonus.startDate) {
      return res.status(400).json({
        success: false,
        message: 'Bonus has not started yet',
        details: {
          startDate: bonus.startDate,
          currentDate: now
        }
      });
    }

    if (bonus.endDate && now > bonus.endDate) {
      return res.status(400).json({
        success: false,
        message: 'Bonus has expired',
        details: {
          endDate: bonus.endDate,
          currentDate: now
        }
      });
    }

    // Check usage limits
    if (bonus.maxTotalUsage && bonus.currentUsageCount >= bonus.maxTotalUsage) {
      return res.status(400).json({
        success: false,
        message: 'Bonus has reached maximum usage limit',
        details: {
          maxUsage: bonus.maxTotalUsage,
          currentUsage: bonus.currentUsageCount
        }
      });
    }

    next();
  } catch (error) {
    console.error('Error validating bonus active status:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating bonus status'
    });
  }
};

/**
 * Middleware to validate campaign is active
 */
export const validateCampaignActive = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const campaign = (req as any).campaign;
    
    if (!campaign) {
      return res.status(400).json({
        success: false,
        message: 'Campaign not found in request context'
      });
    }

    // Check if campaign is active
    if (campaign.status !== CampaignStatus.ACTIVE) {
      return res.status(400).json({
        success: false,
        message: `Campaign is ${campaign.status.toLowerCase()}, not active`
      });
    }

    // Check date range
    const now = new Date();
    
    if (campaign.startDate && now < campaign.startDate) {
      return res.status(400).json({
        success: false,
        message: 'Campaign has not started yet',
        details: {
          startDate: campaign.startDate,
          currentDate: now
        }
      });
    }

    if (campaign.endDate && now > campaign.endDate) {
      return res.status(400).json({
        success: false,
        message: 'Campaign has ended',
        details: {
          endDate: campaign.endDate,
          currentDate: now
        }
      });
    }

    // Check participant limit
    if (campaign.maxParticipants && campaign.currentParticipants >= campaign.maxParticipants) {
      return res.status(400).json({
        success: false,
        message: 'Campaign has reached maximum participants',
        details: {
          maxParticipants: campaign.maxParticipants,
          currentParticipants: campaign.currentParticipants
        }
      });
    }

    // Check budget limit
    if (campaign.budgetLimit && campaign.currentSpent >= campaign.budgetLimit) {
      return res.status(400).json({
        success: false,
        message: 'Campaign has reached budget limit',
        details: {
          budgetLimit: campaign.budgetLimit,
          currentSpent: campaign.currentSpent
        }
      });
    }

    next();
  } catch (error) {
    console.error('Error validating campaign active status:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating campaign status'
    });
  }
};

/**
 * Middleware to validate bonus creation/update data
 */
export const validateBonusData = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { minAmount, maxAmount, startDate, endDate, isPercentage, amount } = req.body;

    // Validate amount constraints
    if (minAmount && maxAmount && minAmount > maxAmount) {
      return res.status(400).json({
        success: false,
        message: 'Minimum amount cannot be greater than maximum amount'
      });
    }

    // Validate date range
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start >= end) {
        return res.status(400).json({
          success: false,
          message: 'Start date must be before end date'
        });
      }

      // Check if start date is not too far in the past
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      if (start < oneDayAgo) {
        return res.status(400).json({
          success: false,
          message: 'Start date cannot be more than 1 day in the past'
        });
      }
    }

    // Validate percentage bonus constraints
    if (isPercentage) {
      if (amount > 100) {
        return res.status(400).json({
          success: false,
          message: 'Percentage bonus cannot exceed 100%'
        });
      }

      if (!maxAmount) {
        return res.status(400).json({
          success: false,
          message: 'Maximum amount is required for percentage bonuses'
        });
      }
    }

    next();
  } catch (error) {
    console.error('Error validating bonus data:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating bonus data'
    });
  }
};

/**
 * Middleware to validate campaign data
 */
export const validateCampaignData = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { startDate, endDate, bonusIds, maxParticipants, budgetLimit } = req.body;

    // Validate date range
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start >= end) {
        return res.status(400).json({
          success: false,
          message: 'Start date must be before end date'
        });
      }

      // Campaign duration should be reasonable (not more than 2 years)
      const maxDurationMs = 2 * 365 * 24 * 60 * 60 * 1000; // 2 years
      if (end.getTime() - start.getTime() > maxDurationMs) {
        return res.status(400).json({
          success: false,
          message: 'Campaign duration cannot exceed 2 years'
        });
      }
    }

    // Validate bonus IDs if provided
    if (bonusIds && Array.isArray(bonusIds) && bonusIds.length > 0) {
      // Check if all bonus IDs are valid MongoDB ObjectIds
      for (const bonusId of bonusIds) {
        if (!mongoose.Types.ObjectId.isValid(bonusId)) {
          return res.status(400).json({
            success: false,
            message: `Invalid bonus ID: ${bonusId}`
          });
        }
      }

      // Check if bonuses exist and are active
      const validBonuses = await BonusModel.find({
        _id: { $in: bonusIds },
        status: BonusStatus.ACTIVE
      });

      if (validBonuses.length !== bonusIds.length) {
        return res.status(400).json({
          success: false,
          message: 'One or more bonus IDs are invalid or inactive'
        });
      }
    }

    // Validate numerical constraints
    if (maxParticipants && maxParticipants < 1) {
      return res.status(400).json({
        success: false,
        message: 'Maximum participants must be at least 1'
      });
    }

    if (budgetLimit && budgetLimit < 0) {
      return res.status(400).json({
        success: false,
        message: 'Budget limit cannot be negative'
      });
    }

    next();
  } catch (error) {
    console.error('Error validating campaign data:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while validating campaign data'
    });
  }
};

// Helper functions

/**
 * Check if user has bonus management permission
 */
async function checkBonusManagementPermission(userId: mongoose.Types.ObjectId): Promise<boolean> {
  try {
    // This would integrate with your RBAC system
    // For now, check if user is admin
    const { UserModel } = require('../models/User');
    const user = await UserModel.findById(userId);
    
    // Check if user has admin role or specific bonus management permission
    const { RoleType } = require('../models/Role');
    return user?.role === RoleType.ADMIN_USER || user?.role === RoleType.SUPER_ADMIN;
  } catch (error) {
    console.error('Error checking bonus management permission:', error);
    return false;
  }
}

/**
 * Check if user can distribute a specific bonus
 */
async function checkBonusDistributionPermission(
  userId: mongoose.Types.ObjectId, 
  bonus: any
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Check basic admin permission
    const hasManagementPermission = await checkBonusManagementPermission(userId);
    if (!hasManagementPermission) {
      return { allowed: false, reason: 'Insufficient admin permissions' };
    }

    // Check if bonus is active
    if (bonus.status !== BonusStatus.ACTIVE) {
      return { allowed: false, reason: 'Bonus is not active' };
    }

    // Check if bonus is within date range
    const now = new Date();
    if (bonus.startDate && now < bonus.startDate) {
      return { allowed: false, reason: 'Bonus has not started yet' };
    }

    if (bonus.endDate && now > bonus.endDate) {
      return { allowed: false, reason: 'Bonus has expired' };
    }

    // Check usage limits
    if (bonus.maxTotalUsage && bonus.currentUsageCount >= bonus.maxTotalUsage) {
      return { allowed: false, reason: 'Bonus has reached maximum usage limit' };
    }

    return { allowed: true };
  } catch (error) {
    console.error('Error checking bonus distribution permission:', error);
    return { allowed: false, reason: 'Internal error while checking permissions' };
  }
}