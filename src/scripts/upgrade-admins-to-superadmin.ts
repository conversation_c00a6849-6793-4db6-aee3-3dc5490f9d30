#!/usr/bin/env ts-node

/**
 * Upgrade Admins to Super Admin Script
 * 
 * This script upgrades all current admin users to have super admin privileges
 * in both the legacy role system and the new RBAC system.
 */

import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import { RBACService } from '../services/rbac.service';
import { RBACManagementService } from '../services/rbac-management.service';
import { RoleType } from '../models/Role';
import { logger } from '../utils/logger';

async function upgradeAdminsToSuperAdmin() {
  try {
    logger.info('🚀 Starting admin upgrade to super admin...');

    // Connect to database
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database');

    // Find all current admin users
    const adminUsers = await User.find({
      role: { $in: [RoleType.ADMIN_USER, RoleType.SUPER_ADMIN] }
    }).select('_id email role fullName');

    if (adminUsers.length === 0) {
      logger.info('ℹ️ No admin users found to upgrade');
      return;
    }

    logger.info(`📋 Found ${adminUsers.length} admin users to process:`);
    adminUsers.forEach(user => {
      logger.info(`   - ${user.email} (${user.fullName}) - Current role: ${user.role}`);
    });

    let upgradedCount = 0;
    let errorCount = 0;

    // Process each admin user
    for (const user of adminUsers) {
      try {
        logger.info(`\n🔄 Processing user: ${user.email}...`);

        // 1. Update legacy role to superadmin
        if (user.role !== RoleType.SUPER_ADMIN) {
          user.role = RoleType.SUPER_ADMIN;
          await user.save();
          logger.info(`   ✅ Updated legacy role to superadmin`);
        } else {
          logger.info(`   ℹ️ Already has superadmin legacy role`);
        }

        // 2. Check if user has RBAC roles
        const existingRoles = await RBACService.getUserActiveRoles(user._id);
        
        // 3. Assign SUPER_ADMIN RBAC role if not already assigned
        const hasSuperAdminRole = existingRoles.some((role: any) => role.roleType === RoleType.SUPER_ADMIN);
        
        if (!hasSuperAdminRole) {
          try {
            // Find a super admin to perform the assignment (or use the system)
            const superAdminUser = await User.findOne({ role: RoleType.SUPER_ADMIN }).limit(1);
            const assignerId = superAdminUser?._id || user._id; // Use self if no other super admin exists

            await RBACManagementService.assignRoleToUser(
              assignerId,
              user._id,
              RoleType.SUPER_ADMIN,
              {
                reason: 'Automatic upgrade from admin to super admin',
                temporary: false
              }
            );
            logger.info(`   ✅ Assigned SUPER_ADMIN RBAC role`);
          } catch (rbacError) {
            // If RBAC assignment fails, try direct assignment
            logger.warn(`   ⚠️ RBAC assignment failed, trying direct assignment...`);
            await RBACService.assignRole(
              user._id,
              RoleType.SUPER_ADMIN,
              user._id, // Self-assignment
              {
                reason: 'Automatic upgrade from admin to super admin',
                temporary: false
              }
            );
            logger.info(`   ✅ Assigned SUPER_ADMIN role via direct assignment`);
          }
        } else {
          logger.info(`   ℹ️ Already has SUPER_ADMIN RBAC role`);
        }

        upgradedCount++;
        logger.info(`   🎉 Successfully processed user: ${user.email}`);

      } catch (userError) {
        errorCount++;
        logger.error(`   ❌ Error processing user ${user.email}:`, userError);
      }
    }

    // Summary
    logger.info(`\n📊 Upgrade Summary:`);
    logger.info(`   ✅ Successfully processed: ${upgradedCount} users`);
    logger.info(`   ❌ Errors: ${errorCount} users`);
    
    if (upgradedCount > 0) {
      logger.info(`\n🎉 Admin upgrade completed successfully!`);
      logger.info(`All admin users now have super admin privileges.`);
    }

  } catch (error) {
    logger.error('❌ Failed to upgrade admins to super admin:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    logger.info('📱 Disconnected from database');
  }
}

// Run the script
if (require.main === module) {
  upgradeAdminsToSuperAdmin()
    .then(() => {
      logger.info('🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export default upgradeAdminsToSuperAdmin; 