#!/usr/bin/env ts-node

/**
 * Sync Legacy Admin Users to RBAC System
 * 
 * This script identifies legacy admin users (those with role 'admin' or 'superadmin' 
 * but no RBAC roles assigned) and assigns them appropriate RBAC roles.
 */

import mongoose from 'mongoose';
import { config } from '../config/config';
import { User, IUser } from '../models/User';
import { Role, RoleType } from '../models/Role';
import { UserRole } from '../models/UserRole';
import { RBACService } from '../services/rbac.service';
import { logger } from '../utils/logger';

interface LegacyAdminSync {
  userId: string;
  email: string;
  legacyRole: string;
  assignedRoles: RoleType[];
  success: boolean;
  error?: string;
}

class LegacyAdminSyncService {
  
  /**
   * Find all legacy admin users without RBAC roles
   */
  async findLegacyAdmins(): Promise<IUser[]> {
    // Find users with admin/superadmin role
    const legacyAdmins = await User.find({
      role: { $in: [RoleType.ADMIN_USER, RoleType.SUPER_ADMIN] }
    }).select('_id email role fullName');

    const legacyAdminsWithoutRBAC = [];

    for (const admin of legacyAdmins) {
      // Check if they have any RBAC roles
      const rbacRoles = await UserRole.find({
        userId: admin._id,
        isActive: true
      });

      if (rbacRoles.length === 0) {
        legacyAdminsWithoutRBAC.push(admin);
        logger.info(`Found legacy admin without RBAC roles: ${admin.email} (${admin.role})`);
      }
    }

    return legacyAdminsWithoutRBAC;
  }

  /**
   * Map legacy roles to RBAC roles
   */
  private mapLegacyToRBACRoles(legacyRole: string): RoleType[] {
    switch (legacyRole) {
      case RoleType.SUPER_ADMIN:
        return [RoleType.SUPER_ADMIN, RoleType.MAJOR_ADMIN, RoleType.ADMIN_USER];
      case RoleType.ADMIN_USER:
        return [RoleType.MAJOR_ADMIN, RoleType.ADMIN_USER];
      default:
        return [RoleType.REGULAR_USER];
    }
  }

  /**
   * Sync a single legacy admin to RBAC
   */
  async syncLegacyAdmin(admin: IUser): Promise<LegacyAdminSync> {
    const result: LegacyAdminSync = {
      userId: admin._id.toString(),
      email: admin.email,
      legacyRole: admin.role || RoleType.REGULAR_USER,
      assignedRoles: [],
      success: false
    };

    try {
      const rolesToAssign = this.mapLegacyToRBACRoles(admin.role || 'user');
      
      // Use system user for assignment (use the admin's own ID since this is a migration)
      const systemUserId = admin._id;

      for (const roleType of rolesToAssign) {
        try {
          // Check if role exists
          const roleExists = await Role.findOne({ name: roleType, isActive: true });
          if (!roleExists) {
            logger.warn(`Role ${roleType} not found, skipping for user ${admin.email}`);
            continue;
          }

          // Check if user already has this role
          const existingRole = await UserRole.findOne({
            userId: admin._id,
            roleType,
            isActive: true
          });

          if (existingRole) {
            logger.info(`User ${admin.email} already has role ${roleType}, skipping`);
            result.assignedRoles.push(roleType);
            continue;
          }

          // Assign the role
          await RBACService.assignRole(
            admin._id,
            roleType,
            systemUserId, // System assignment
            {
              reason: `Legacy admin migration from role: ${admin.role}`,
              temporary: false,
              emergencyAccess: false
            }
          );

          result.assignedRoles.push(roleType);
          logger.info(`Assigned role ${roleType} to legacy admin ${admin.email}`);

        } catch (roleError) {
          logger.error(`Failed to assign role ${roleType} to ${admin.email}:`, roleError);
          result.error = roleError instanceof Error ? roleError.message : 'Unknown role assignment error';
        }
      }

      if (result.assignedRoles.length > 0) {
        result.success = true;
        logger.info(`Successfully synced legacy admin ${admin.email} with ${result.assignedRoles.length} roles`);
      } else {
        result.error = 'No roles were assigned';
      }

    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to sync legacy admin ${admin.email}:`, error);
    }

    return result;
  }

  /**
   * Sync all legacy admins to RBAC system
   */
  async syncAllLegacyAdmins(): Promise<{
    total: number;
    successful: number;
    failed: number;
    results: LegacyAdminSync[];
  }> {
    logger.info('Starting legacy admin sync to RBAC system...');

    const legacyAdmins = await this.findLegacyAdmins();
    const results: LegacyAdminSync[] = [];

    logger.info(`Found ${legacyAdmins.length} legacy admins without RBAC roles`);

    for (const admin of legacyAdmins) {
      const result = await this.syncLegacyAdmin(admin);
      results.push(result);
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    const summary = {
      total: legacyAdmins.length,
      successful,
      failed,
      results
    };

    logger.info('Legacy admin sync completed', summary);
    return summary;
  }

  /**
   * Sync a specific user by email
   */
  async syncSpecificAdmin(email: string): Promise<LegacyAdminSync> {
    const admin = await User.findOne({ 
      email,
      role: { $in: ['admin', 'superadmin'] }
    });

    if (!admin) {
      throw new Error(`Admin user with email ${email} not found`);
    }

    // Check if already has RBAC roles
    const existingRoles = await UserRole.find({
      userId: admin._id,
      isActive: true
    });

    if (existingRoles.length > 0) {
      logger.info(`Admin ${email} already has RBAC roles:`, existingRoles.map(r => r.roleType));
      return {
        userId: admin._id.toString(),
        email: admin.email,
        legacyRole: admin.role || 'user',
        assignedRoles: existingRoles.map(r => r.roleType),
        success: true
      };
    }

    return await this.syncLegacyAdmin(admin);
  }
}

async function main() {
  try {
    const args = process.argv.slice(2);
    const emailArg = args.find(arg => arg.startsWith('--email='))?.split('=')[1];
    const allFlag = args.includes('--all');

    if (!emailArg && !allFlag) {
      console.error('Usage: npm run sync:legacy-admin -- [--email=<admin-email> | --all]');
      console.error('Examples:');
      console.error('  npm run sync:legacy-admin -- --email=<EMAIL>');
      console.error('  npm run sync:legacy-admin -- --all');
      process.exit(1);
    }

    // Connect to database
    await mongoose.connect(config.MONGODB_URI);
    logger.info('Connected to database');

    const syncService = new LegacyAdminSyncService();

    if (emailArg) {
      // Sync specific admin
      logger.info(`Syncing specific admin: ${emailArg}`);
      const result = await syncService.syncSpecificAdmin(emailArg);
      
      console.log('\n=== SYNC RESULT ===');
      console.log(`Email: ${result.email}`);
      console.log(`Legacy Role: ${result.legacyRole}`);
      console.log(`Success: ${result.success}`);
      console.log(`Assigned Roles: ${result.assignedRoles.join(', ')}`);
      if (result.error) {
        console.log(`Error: ${result.error}`);
      }
    } else {
      // Sync all legacy admins
      const summary = await syncService.syncAllLegacyAdmins();
      
      console.log('\n=== SYNC SUMMARY ===');
      console.log(`Total Legacy Admins: ${summary.total}`);
      console.log(`Successfully Synced: ${summary.successful}`);
      console.log(`Failed: ${summary.failed}`);
      
      if (summary.results.length > 0) {
        console.log('\n=== DETAILED RESULTS ===');
        summary.results.forEach(result => {
          console.log(`\n${result.email} (${result.legacyRole}):`);
          console.log(`  Success: ${result.success}`);
          console.log(`  Roles: ${result.assignedRoles.join(', ')}`);
          if (result.error) {
            console.log(`  Error: ${result.error}`);
          }
        });
      }
    }

    logger.info('Legacy admin sync completed successfully!');

  } catch (error) {
    logger.error('Legacy admin sync failed:', error);
    console.error('Error:', error instanceof Error ? error.message : error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

// Run if executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { LegacyAdminSyncService }; 