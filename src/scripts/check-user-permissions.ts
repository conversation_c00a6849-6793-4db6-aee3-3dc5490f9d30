#!/usr/bin/env ts-node

/**
 * Check User Permissions Script
 * 
 * This script checks a specific user's permissions and role assignments
 */

import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import { Role, RoleType } from '../models/Role';
import { UserRole } from '../models/UserRole';
import { RBACService } from '../services/rbac.service';
import { logger } from '../utils/logger';

class UserPermissionChecker {
  
  async checkUserPermissions(userId: string): Promise<void> {
    try {
      logger.info(`🔍 Checking permissions for user: ${userId}`);
      
      // Find user
      const user = await User.findById(userId);
      if (!user) {
        logger.error(`❌ User not found: ${userId}`);
        return;
      }

      logger.info(`👤 User found: ${user.email} (${user.fullName})`);
      logger.info(`🏷️  Legacy role: ${user.role}`);

      // Check RBAC roles
      const userRoles = await UserRole.find({ userId: user._id, isActive: true });
      logger.info(`🎭 RBAC Roles (${userRoles.length}):`);
      
      for (const userRole of userRoles) {
        const role = await Role.findOne({ name: userRole.roleType });
        if (role) {
          logger.info(`  - ${userRole.roleType} (Level: ${role.level})`);
          logger.info(`    Permissions: ${role.permissions.length} total`);
          
          // Check for manufacturing orders permissions
          const manufacturingPermissions = role.permissions.filter(p => p.includes('manufacturing-orders'));
          if (manufacturingPermissions.length > 0) {
            logger.info(`    Manufacturing Orders Permissions:`);
            manufacturingPermissions.forEach(perm => {
              logger.info(`      ✅ ${perm}`);
            });
          } else {
            logger.warn(`    ⚠️  No manufacturing orders permissions found`);
          }
        }
      }

      // Check specific manufacturing orders permissions
      const manufacturingPermissions = [
        'manufacturing-orders.create',
        'manufacturing-orders.read',
        'manufacturing-orders.update',
        'manufacturing-orders.delete',
        'manufacturing-orders.manage',
        'manufacturing-orders.audit'
      ];

      logger.info(`🔐 Manufacturing Orders Permission Check:`);
      for (const permission of manufacturingPermissions) {
        const hasPermission = await RBACService.hasPermission(user._id, permission);
        const status = hasPermission ? '✅' : '❌';
        logger.info(`  ${status} ${permission}`);
      }

      // Check role level
      const userLevel = await RBACService.getUserHighestRoleLevel(user._id);
      logger.info(`📊 Highest Role Level: ${userLevel}`);

      // Check if user should have admin access
      const isLegacyAdmin = user.role === RoleType.ADMIN_USER || user.role === RoleType.SUPER_ADMIN;
      logger.info(`👑 Legacy Admin Status: ${isLegacyAdmin ? '✅ Yes' : '❌ No'}`);

    } catch (error) {
      logger.error('❌ Error checking user permissions:', error);
    }
  }

  async findUserByEmail(email: string): Promise<void> {
    try {
      const user = await User.findOne({ email });
      if (!user) {
        logger.error(`❌ User not found with email: ${email}`);
        return;
      }

      await this.checkUserPermissions(user._id.toString());
    } catch (error) {
      logger.error('❌ Error finding user by email:', error);
    }
  }

  async run(): Promise<void> {
    try {
      logger.info('🚀 Starting User Permission Check...');
      
      // Connect to database
      await mongoose.connect(config.MONGODB_URI);
      logger.info('📦 Connected to database');

      // Get user ID from command line args or use default admin email
      const args = process.argv.slice(2);
      const userIdentifier = args[0];

      if (!userIdentifier) {
        logger.error('❌ Please provide a user ID or email as an argument');
        logger.info('Usage: npm run check-user-permissions <userId|email>');
        return;
      }

      // Check if it's an email or user ID
      if (userIdentifier.includes('@')) {
        await this.findUserByEmail(userIdentifier);
      } else {
        await this.checkUserPermissions(userIdentifier);
      }
      
      logger.info('✅ User Permission Check completed!');
      
    } catch (error) {
      logger.error('❌ User Permission Check failed:', error);
      throw error;
    } finally {
      await mongoose.disconnect();
      logger.info('📦 Disconnected from database');
    }
  }
}

// Run the script if called directly
if (require.main === module) {
  const checker = new UserPermissionChecker();
  checker.run()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

export default UserPermissionChecker;
