import { Request } from 'express';
import { Document } from 'mongoose';
import { IProfile } from '../interfaces/profile.interface';
import { RoleType } from '../models/Role';

// Legacy role type - deprecated, use RoleType enum instead
export type Role = RoleType;

declare global {
  namespace Express {
    interface Request {
      profile?: Document<unknown, {}, IProfile> & IProfile;
    }
  }
}




// {
//   "profileCategory": "individual",
//   "profileType": "dummy",
//   "name": "dummy Professional Profile Template",
//   "slug": "dummy-professional-template",
//   "categories": [
//     {
//       "name": "basic_info",
//       "label": "Basic Information",
//       "icon": "user",
//       "collapsible": false,
//       "fields": [
//         {
//           "name": "full_name",
//           "label": "Full Name",
//           "widget": "text",
//           "order": 1,
//           "enabled": true,
//           "required": true,
//           "placeholder": "Enter your full name",
//           "validation": {
//             "min": 2,
//             "max": 100
//           }
//         },
//         {
//           "name": "job_title",
//           "label": "Job Title",
//           "widget": "text",
//           "order": 2,
//           "enabled": true,
//           "required": true,
//           "placeholder": "Your professional title"
//         }
//       ]
//     },
//     {
//       "name": "contact",
//       "label": "Contact Information",
//       "icon": "phone",
//       "collapsible": true,
//       "fields": [
//         {
//           "name": "email",
//           "label": "Email Address",
//           "widget": "email",
//           "order": 1,
//           "enabled": true,
//           "required": true,
//           "placeholder": "<EMAIL>",
//           "validation": {
//             "regex": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
//           }
//         },
//         {
//           "name": "phone",
//           "label": "Phone Number",
//           "widget": "phone",
//           "order": 2,
//           "enabled": true,
//           "placeholder": "+1234567890"
//         },
//         {
//           "name": "social_links",
//           "label": "Social Media Links",
//           "widget": "list:text",
//           "order": 3,
//           "enabled": true,
//           "placeholder": "https://linkedin.com/in/yourprofile"
//         }
//       ]
//     },
//     {
//       "name": "professional",
//       "label": "Professional Details",
//       "icon": "briefcase",
//       "fields": [
//         {
//           "name": "skills",
//           "label": "Skills",
//           "widget": "multiselect",
//           "order": 1,
//           "enabled": true,
//           "options": [
//             { "label": "JavaScript", "value": "js" },
//             { "label": "TypeScript", "value": "ts" },
//             { "label": "React", "value": "react" }
//           ]
//         },
//         {
//           "name": "experience",
//           "label": "Years of Experience",
//           "widget": "number",
//           "order": 2,
//           "enabled": true,
//           "validation": {
//             "min": 0,
//             "max": 50
//           }
//         },
//         {
//           "name": "resume",
//           "label": "Upload Resume",
//           "widget": "file",
//           "order": 3,
//           "enabled": false
//         }
//       ]
//     }
//   ]
// }
// 681a5eaa74eed7c84b9778dd