/**
 * @file activity-analytics.service.ts
 * @description Activity Analytics and Reporting Dashboard Service
 * ============================================================
 * 
 * This service provides comprehensive analytics, insights, and reporting
 * capabilities for activity data with chart-ready data structures,
 * performance metrics, and business intelligence features.
 */

import mongoose from 'mongoose';
import { AccountActivityModel } from '../models/account-activity.model';
import { ProfileActivityModel } from '../models/gamification/user-activity.model';
import { 
  IAccountActivity, 
  IProfileActivity,
  AccountActivityCategory,
  ProfileActivityCategory,
  ActivityVisibility 
} from '../interfaces/activity-log.interface';
import { ActivityType } from '../constants/activity-types';
import { logger } from '../utils/logger';
import { RoleType } from '../models/Role';

interface TimeRange {
  start: Date;
  end: Date;
  label?: string;
}

interface AnalyticsOptions {
  timeRange: TimeRange;
  granularity: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
  includeComparisons?: boolean;
  includePredictions?: boolean;
  includeSegmentation?: boolean;
}

interface ChartDataPoint {
  label: string;
  value: number;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

interface ActivityTrend {
  period: string;
  count: number;
  growth: number;
  activityTypes: Record<string, number>;
  categories: Record<string, number>;
}

interface UserEngagementMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  returningUsers: number;
  averageSessionDuration: number;
  activitiesPerUser: number;
  engagementScore: number;
  churnRate: number;
}

interface MyPtsAnalytics {
  totalEarned: number;
  totalSpent: number;
  averagePerUser: number;
  topEarners: Array<{ userId: string; points: number; rank: number }>;
  rewardDistribution: Array<{ activityType: string; totalPoints: number; count: number }>;
  pointsFlow: Array<{ period: string; earned: number; spent: number; net: number }>;
}

interface PlatformInsights {
  mostActiveFeatures: Array<{ feature: string; usage: number; growth: number }>;
  userBehaviorPatterns: Array<{ pattern: string; frequency: number; userSegment: string }>;
  conversionFunnels: Array<{ step: string; users: number; conversionRate: number }>;
  retentionCohorts: Array<{ cohort: string; retention: number[]; size: number }>;
}

interface ActivityHeatmap {
  dayOfWeek: Array<{ day: string; hours: number[] }>;
  monthlyPattern: Array<{ month: string; dailyActivity: number[] }>;
  geographicDistribution: Array<{ region: string; activity: number; users: number }>;
}

interface ComprehensiveAnalyticsReport {
  summary: {
    totalActivities: number;
    uniqueUsers: number;
    timeRange: TimeRange;
    generatedAt: Date;
  };
  activityTrends: ActivityTrend[];
  userEngagement: UserEngagementMetrics;
  myPtsAnalytics: MyPtsAnalytics;
  platformInsights: PlatformInsights;
  activityHeatmap: ActivityHeatmap;
  chartData: {
    activityOverTime: ChartDataPoint[];
    topActivityTypes: ChartDataPoint[];
    userEngagementTrend: ChartDataPoint[];
    myPtsDistribution: ChartDataPoint[];
    categoryBreakdown: ChartDataPoint[];
  };
  comparisons?: {
    previousPeriod: {
      activitiesGrowth: number;
      usersGrowth: number;
      engagementGrowth: number;
    };
  };
}

export class ActivityAnalyticsService {
  private cacheTTL: number = 5 * 60 * 1000; // 5 minutes
  private analyticsCache: Map<string, { data: any; timestamp: number }>;

  constructor() {
    this.analyticsCache = new Map();
    
    // Clean cache every 10 minutes
    setInterval(() => this.cleanCache(), 10 * 60 * 1000);
  }

  // ============================================================================
  // Main Analytics Methods
  // ============================================================================

  /**
   * Generate comprehensive analytics report
   */
  async generateComprehensiveReport(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: RoleType = RoleType.REGULAR_USER
  ): Promise<ComprehensiveAnalyticsReport> {
    const startTime = Date.now();
    const cacheKey = `comprehensive_${JSON.stringify(options)}_${userId?.toString() || 'global'}_${userRole}`;

    try {
      // Check cache first
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      // Generate fresh report
      const [
        summary,
        activityTrends,
        userEngagement,
        myPtsAnalytics,
        platformInsights,
        activityHeatmap,
        chartData,
        comparisons
      ] = await Promise.all([
        this.generateSummary(options, userId, userRole),
        this.generateActivityTrends(options, userId, userRole),
        this.generateUserEngagementMetrics(options, userId, userRole),
        this.generateMyPtsAnalytics(options, userId, userRole),
        this.generatePlatformInsights(options, userId, userRole),
        this.generateActivityHeatmap(options, userId, userRole),
        this.generateChartData(options, userId, userRole),
        options.includeComparisons ? this.generateComparisons(options, userId, userRole) : null
      ]);

      const report: ComprehensiveAnalyticsReport = {
        summary,
        activityTrends,
        userEngagement,
        myPtsAnalytics,
        platformInsights,
        activityHeatmap,
        chartData,
        ...(comparisons && { comparisons })
      };

      // Cache the result
      this.setCache(cacheKey, report);

      logger.info('Comprehensive analytics report generated', {
        userId: userId?.toString(),
        userRole,
        timeRange: options.timeRange,
        generationTime: Date.now() - startTime
      });

      return report;

    } catch (error) {
      logger.error('Error generating comprehensive analytics report:', error);
      throw new Error('Analytics report generation failed');
    }
  }

  /**
   * Generate user engagement dashboard
   */
  async generateUserEngagementDashboard(
    timeRange: TimeRange,
    userRole: RoleType = RoleType.ADMIN_USER
  ): Promise<{
    overview: UserEngagementMetrics;
    dailyEngagement: ChartDataPoint[];
    featureUsage: ChartDataPoint[];
    userJourney: Array<{ step: string; users: number; dropoffRate: number }>;
    segmentAnalysis: Array<{ segment: string; metrics: UserEngagementMetrics }>;
  }> {
    try {
      const baseQuery = this.buildBaseQuery(timeRange, undefined, userRole);

      // Generate engagement overview
      const overview = await this.generateUserEngagementMetrics({
        timeRange,
        granularity: 'day'
      }, undefined, userRole);

      // Daily engagement trend
      const dailyEngagement = await this.generateDailyEngagementTrend([timeRange.start, timeRange.end], baseQuery);

      // Feature usage analysis
      const featureUsage = await this.generateFeatureUsageAnalysis(timeRange, baseQuery);

      // User journey analysis
      const userJourney = await this.generateUserJourneyAnalysis(timeRange, baseQuery);

      // Segment analysis
      const segmentAnalysis = await this.generateSegmentAnalysis(timeRange, baseQuery);

      return {
        overview,
        dailyEngagement,
        featureUsage,
        userJourney,
        segmentAnalysis
      };

    } catch (error) {
      logger.error('Error generating user engagement dashboard:', error);
      throw new Error('User engagement dashboard generation failed');
    }
  }

  /**
   * Generate MyPts performance dashboard
   */
  async generateMyPtsDashboard(
    timeRange: TimeRange,
    userId?: mongoose.Types.ObjectId
  ): Promise<{
    overview: MyPtsAnalytics;
    earningTrends: ChartDataPoint[];
    spendingTrends: ChartDataPoint[];
    rewardEffectiveness: Array<{ activityType: string; conversionRate: number; avgPoints: number }>;
    userLeaderboard: Array<{ userId: string; username: string; totalPoints: number; rank: number }>;
  }> {
    try {
      const overview = await this.generateMyPtsAnalytics({
        timeRange,
        granularity: 'day'
      }, userId);

      const [earningTrends, spendingTrends, rewardEffectiveness, userLeaderboard] = await Promise.all([
        this.generateMyPtsEarningTrends(timeRange, userId),
        this.generateMyPtsSpendingTrends(timeRange, userId),
        this.generateRewardEffectiveness(timeRange),
        this.generateUserLeaderboard(timeRange, 100)
      ]);

      return {
        overview,
        earningTrends,
        spendingTrends,
        rewardEffectiveness,
        userLeaderboard
      };

    } catch (error) {
      logger.error('Error generating MyPts dashboard:', error);
      throw new Error('MyPts dashboard generation failed');
    }
  }

  /**
   * Generate real-time activity feed analytics
   */
  async generateRealTimeAnalytics(): Promise<{
    liveActivityCount: number;
    topActivityTypes: ChartDataPoint[];
    userDistribution: ChartDataPoint[];
    performanceMetrics: {
      avgResponseTime: number;
      throughput: number;
      errorRate: number;
    };
    systemHealth: {
      status: 'healthy' | 'warning' | 'critical';
      metrics: Record<string, number>;
    };
  }> {
    try {
      const last5Minutes = new Date(Date.now() - 5 * 60 * 1000);
      const last1Hour = new Date(Date.now() - 60 * 60 * 1000);

      const [
        liveActivityCount,
        topActivityTypes,
        userDistribution,
        performanceMetrics
      ] = await Promise.all([
        this.getLiveActivityCount(last5Minutes),
        this.getTopActivityTypes(last1Hour),
        this.getUserDistribution(last1Hour),
        this.getPerformanceMetrics(last5Minutes)
      ]);

      const systemHealth = this.assessSystemHealth(performanceMetrics, liveActivityCount);

      return {
        liveActivityCount,
        topActivityTypes,
        userDistribution,
        performanceMetrics,
        systemHealth
      };

    } catch (error) {
      logger.error('Error generating real-time analytics:', error);
      throw new Error('Real-time analytics generation failed');
    }
  }

  // ============================================================================
  // Specialized Analytics Methods
  // ============================================================================

  private async generateSummary(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<any> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);

    const [accountCount, profileCount, uniqueUsers] = await Promise.all([
      AccountActivityModel.countDocuments(baseQuery),
      ProfileActivityModel.countDocuments(baseQuery),
      AccountActivityModel.distinct('userId', baseQuery).then(users => users.length)
    ]);

    return {
      totalActivities: accountCount + profileCount,
      uniqueUsers,
      timeRange: options.timeRange,
      generatedAt: new Date()
    };
  }

  private async generateActivityTrends(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<ActivityTrend[]> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    const groupBy = this.getGroupByStage(options.granularity);

    const [accountTrends, profileTrends] = await Promise.all([
      AccountActivityModel.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: groupBy,
            count: { $sum: 1 },
            activityTypes: {
              $push: '$activityType'
            },
            categories: {
              $push: '$category'
            }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]),
      ProfileActivityModel.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: groupBy,
            count: { $sum: 1 },
            activityTypes: {
              $push: '$activityType'
            },
            categories: {
              $push: '$category'
            }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ])
    ]);

    // Combine and process trends
    return this.processTrendData(accountTrends, profileTrends, options.granularity);
  }

  private async generateUserEngagementMetrics(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<UserEngagementMetrics> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);

    const [
      totalUsers,
      activeUsers,
      newUsers,
      sessionData,
      activityData
    ] = await Promise.all([
      AccountActivityModel.distinct('userId', baseQuery).then(users => users.length),
      this.getActiveUsers(options.timeRange, baseQuery),
      this.getNewUsers(options.timeRange, baseQuery),
      this.getSessionMetrics(options.timeRange, baseQuery),
      this.getActivityMetrics(options.timeRange, baseQuery)
    ]);

    return {
      totalUsers,
      activeUsers: activeUsers.length,
      newUsers: newUsers.length,
      returningUsers: activeUsers.length - newUsers.length,
      averageSessionDuration: sessionData.avgDuration,
      activitiesPerUser: activityData.avgActivitiesPerUser,
      engagementScore: this.calculateEngagementScore(activeUsers.length, totalUsers, activityData.avgActivitiesPerUser),
      churnRate: this.calculateChurnRate(totalUsers, activeUsers.length)
    };
  }

  private async generateMyPtsAnalytics(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<MyPtsAnalytics> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);

    const pointsData = await ProfileActivityModel.aggregate([
      { $match: { ...baseQuery, $or: [{ myPtsEarned: { $gt: 0 } }, { myPtsSpent: { $gt: 0 } }] } },
      {
        $group: {
          _id: null,
          totalEarned: { $sum: '$myPtsEarned' },
          totalSpent: { $sum: '$myPtsSpent' },
          uniqueUsers: { $addToSet: '$userId' },
          activities: { $push: { activityType: '$activityType', earned: '$myPtsEarned', spent: '$myPtsSpent' } }
        }
      }
    ]);

    const data = pointsData[0] || { totalEarned: 0, totalSpent: 0, uniqueUsers: [], activities: [] };

    const [topEarners, rewardDistribution, pointsFlow] = await Promise.all([
      this.getTopPointEarners(options.timeRange, 10),
      this.getRewardDistribution(options.timeRange),
      this.getPointsFlow(options.timeRange, options.granularity)
    ]);

    return {
      totalEarned: data.totalEarned,
      totalSpent: data.totalSpent,
      averagePerUser: data.uniqueUsers.length > 0 ? data.totalEarned / data.uniqueUsers.length : 0,
      topEarners,
      rewardDistribution,
      pointsFlow
    };
  }

  private async generateChartData(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<any> {
    const [
      activityOverTime,
      topActivityTypes,
      userEngagementTrend,
      myPtsDistribution,
      categoryBreakdown
    ] = await Promise.all([
      this.getActivityOverTimeChart(options, userId, userRole),
      this.getTopActivityTypesChart(options, userId, userRole),
      this.getUserEngagementTrendChart(options, userId, userRole),
      this.getMyPtsDistributionChart(options, userId, userRole),
      this.getCategoryBreakdownChart(options, userId, userRole)
    ]);

    return {
      activityOverTime,
      topActivityTypes,
      userEngagementTrend,
      myPtsDistribution,
      categoryBreakdown
    };
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private buildBaseQuery(
    timeRange: TimeRange,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): any {
    const query: any = {
      timestamp: {
        $gte: timeRange.start,
        $lte: timeRange.end
      }
    };

    if (userId) {
      query.userId = userId;
    }

    // Apply role-based filtering
    if (userRole === 'user' && userId) {
      query.userId = userId;
    }

    return query;
  }

  private getGroupByStage(granularity: string): any {
    switch (granularity) {
      case 'hour':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' },
          hour: { $hour: '$timestamp' }
        };
      case 'day':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' }
        };
      case 'week':
        return {
          year: { $year: '$timestamp' },
          week: { $week: '$timestamp' }
        };
      case 'month':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' }
        };
      case 'quarter':
        return {
          year: { $year: '$timestamp' },
          quarter: { $ceil: { $divide: [{ $month: '$timestamp' }, 3] } }
        };
      case 'year':
        return {
          year: { $year: '$timestamp' }
        };
      default:
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' }
        };
    }
  }

  private processTrendData(accountTrends: any[], profileTrends: any[], granularity: string): ActivityTrend[] {
    const combinedMap = new Map<string, any>();

    // Combine account and profile trends
    [...accountTrends, ...profileTrends].forEach(trend => {
      const key = this.generatePeriodKey(trend._id, granularity);
      
      if (!combinedMap.has(key)) {
        combinedMap.set(key, {
          period: key,
          count: 0,
          growth: 0,
          activityTypes: {},
          categories: {}
        });
      }

      const existing = combinedMap.get(key)!;
      existing.count += trend.count;

      // Count activity types
      trend.activityTypes.forEach((type: string) => {
        existing.activityTypes[type] = (existing.activityTypes[type] || 0) + 1;
      });

      // Count categories
      trend.categories.forEach((category: string) => {
        existing.categories[category] = (existing.categories[category] || 0) + 1;
      });
    });

    const trends = Array.from(combinedMap.values()).sort((a, b) => a.period.localeCompare(b.period));

    // Calculate growth rates
    for (let i = 1; i < trends.length; i++) {
      const current = trends[i];
      const previous = trends[i - 1];
      current.growth = previous.count > 0 ? ((current.count - previous.count) / previous.count) * 100 : 0;
    }

    return trends;
  }

  private generatePeriodKey(groupId: any, granularity: string): string {
    switch (granularity) {
      case 'hour':
        return `${groupId.year}-${groupId.month.toString().padStart(2, '0')}-${groupId.day.toString().padStart(2, '0')} ${groupId.hour.toString().padStart(2, '0')}:00`;
      case 'day':
        return `${groupId.year}-${groupId.month.toString().padStart(2, '0')}-${groupId.day.toString().padStart(2, '0')}`;
      case 'week':
        return `${groupId.year}-W${groupId.week.toString().padStart(2, '0')}`;
      case 'month':
        return `${groupId.year}-${groupId.month.toString().padStart(2, '0')}`;
      case 'quarter':
        return `${groupId.year}-Q${groupId.quarter}`;
      case 'year':
        return `${groupId.year}`;
      default:
        return `${groupId.year}-${groupId.month.toString().padStart(2, '0')}-${groupId.day.toString().padStart(2, '0')}`;
    }
  }

  // Cache management
  private getFromCache(key: string): any | null {
    const cached = this.analyticsCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.analyticsCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  private cleanCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.analyticsCache.entries()) {
      if (now - cached.timestamp > this.cacheTTL) {
        this.analyticsCache.delete(key);
      }
    }
  }

  // Placeholder methods for specific analytics calculations
  private async getActiveUsers(timeRange: TimeRange, baseQuery: any): Promise<any[]> {
    return AccountActivityModel.distinct('userId', baseQuery);
  }

  private async getNewUsers(timeRange: TimeRange, baseQuery: any): Promise<any[]> {
    // Implementation would check user creation dates
    return [];
  }

  private async getSessionMetrics(timeRange: TimeRange, baseQuery: any): Promise<{ avgDuration: number }> {
    // Implementation would calculate session metrics
    return { avgDuration: 0 };
  }

  private async getActivityMetrics(timeRange: TimeRange, baseQuery: any): Promise<{ avgActivitiesPerUser: number }> {
    // Implementation would calculate activity metrics
    return { avgActivitiesPerUser: 0 };
  }

  private calculateEngagementScore(activeUsers: number, totalUsers: number, avgActivitiesPerUser: number): number {
    if (totalUsers === 0) return 0;
    const activityScore = Math.min(avgActivitiesPerUser / 10, 1); // Normalize to 0-1
    const activeUserScore = activeUsers / totalUsers;
    return (activityScore * 0.6 + activeUserScore * 0.4) * 100;
  }

  private calculateChurnRate(totalUsers: number, activeUsers: number): number {
    if (totalUsers === 0) return 0;
    return ((totalUsers - activeUsers) / totalUsers) * 100;
  }

  // Additional placeholder methods that would be implemented based on specific requirements
  private async generatePlatformInsights(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<PlatformInsights> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    
    // Get most active features from metadata
    const [accountFeatures, profileFeatures] = await Promise.all([
      AccountActivityModel.aggregate([
        { $match: baseQuery },
        { $unwind: { path: '$metadata.feature', preserveNullAndEmptyArrays: true } },
        { $group: { _id: '$metadata.feature', usage: { $sum: 1 } } },
        { $match: { _id: { $ne: null } } },
        { $sort: { usage: -1 } },
        { $limit: 10 }
      ]),
      ProfileActivityModel.aggregate([
        { $match: baseQuery },
        { $unwind: { path: '$metadata.feature', preserveNullAndEmptyArrays: true } },
        { $group: { _id: '$metadata.feature', usage: { $sum: 1 } } },
        { $match: { _id: { $ne: null } } },
        { $sort: { usage: -1 } },
        { $limit: 10 }
      ])
    ]);

    const combinedFeatures = [...accountFeatures, ...profileFeatures]
      .reduce((acc: any[], feature) => {
        const existing = acc.find(f => f.feature === feature._id);
        if (existing) {
          existing.usage += feature.usage;
        } else {
          acc.push({ feature: feature._id, usage: feature.usage, growth: 0 });
        }
        return acc;
      }, [])
      .sort((a, b) => b.usage - a.usage)
      .slice(0, 10);

    return {
      mostActiveFeatures: combinedFeatures,
      userBehaviorPatterns: [],
      conversionFunnels: [],
      retentionCohorts: []
    };
  }

  private async generateActivityHeatmap(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<ActivityHeatmap> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    
    // Generate day of week patterns
    const dayOfWeekData = await AccountActivityModel.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: {
            dayOfWeek: { $dayOfWeek: '$timestamp' },
            hour: { $hour: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.dayOfWeek': 1, '_id.hour': 1 } }
    ]);

    // Process day of week data
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayOfWeek = dayNames.map((day, index) => {
      const hours = new Array(24).fill(0);
      dayOfWeekData
        .filter(d => d._id.dayOfWeek === index + 1)
        .forEach(d => {
          hours[d._id.hour] = d.count;
        });
      return { day, hours };
    });

    return {
      dayOfWeek,
      monthlyPattern: [],
      geographicDistribution: []
    };
  }

  private async generateComparisons(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<any> {
    const currentQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    
    // Calculate previous period dates
    const currentStart = options.timeRange.start;
    const currentEnd = options.timeRange.end;
    const periodDuration = currentEnd.getTime() - currentStart.getTime();
    const previousStart = new Date(currentStart.getTime() - periodDuration);
    const previousEnd = new Date(currentStart.getTime());
    
    const previousQuery = this.buildBaseQuery(
      { start: previousStart, end: previousEnd },
      userId,
      userRole
    );

    const [currentStats, previousStats] = await Promise.all([
      this.getBasicStats(currentQuery),
      this.getBasicStats(previousQuery)
    ]);

    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      previousPeriod: {
        activitiesGrowth: calculateGrowth(currentStats.activities, previousStats.activities),
        usersGrowth: calculateGrowth(currentStats.users, previousStats.users),
        engagementGrowth: calculateGrowth(currentStats.engagement, previousStats.engagement)
      }
    };
  }

  // Helper method for basic statistics
  private async getBasicStats(query: any): Promise<{ activities: number; users: number; engagement: number }> {
    const [accountStats, profileStats] = await Promise.all([
      AccountActivityModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            activities: { $sum: 1 },
            users: { $addToSet: '$userId' }
          }
        }
      ]),
      ProfileActivityModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            activities: { $sum: 1 },
            users: { $addToSet: '$userId' },
            totalMyPts: { $sum: '$myPtsEarned' }
          }
        }
      ])
    ]);

    const accountData = accountStats[0] || { activities: 0, users: [] };
    const profileData = profileStats[0] || { activities: 0, users: [], totalMyPts: 0 };
    
    return {
      activities: accountData.activities + profileData.activities,
      users: new Set([...accountData.users, ...profileData.users]).size,
      engagement: profileData.totalMyPts || 0
    };
  }

  // Chart data generation methods
  private async getActivityOverTimeChart(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<ChartDataPoint[]> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    const groupBy = this.getGroupByStage(options.granularity);
    
    const timeData = await AccountActivityModel.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: groupBy,
          value: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    return timeData.map(item => ({
      label: this.generatePeriodKey(item._id, options.granularity),
      value: item.value,
      timestamp: new Date(item._id.year, (item._id.month || 1) - 1, item._id.day || 1)
    }));
  }
  
  private async getTopActivityTypesChart(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<ChartDataPoint[]> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    
    const [accountTypes, profileTypes] = await Promise.all([
      AccountActivityModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$activityType', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      ProfileActivityModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$activityType', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ])
    ]);

    const combined = [...accountTypes, ...profileTypes].reduce((acc: any[], type) => {
      const existing = acc.find(t => t.label === type._id);
      if (existing) {
        existing.value += type.count;
      } else {
        acc.push({ label: type._id, value: type.count });
      }
      return acc;
    }, []);

    return combined.sort((a, b) => b.value - a.value).slice(0, 10);
  }

  private async generateDailyEngagementTrend(
    timeRange?: Date[],
    baseQuery?: any
  ): Promise<ChartDataPoint[]> {
    const query = baseQuery || this.buildBaseQuery(
      timeRange ? { start: timeRange[0], end: timeRange[1] } : { start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), end: new Date() }
    );
    
    const pipeline = [
      { $match: query },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          activities: '$count',
          users: { $size: '$uniqueUsers' }
        }
      },
      { $sort: { date: 1 as 1 } }
    ];

    const [accountTrends, profileTrends] = await Promise.all([
      AccountActivityModel.aggregate(pipeline),
      ProfileActivityModel.aggregate(pipeline)
    ]);

    // Combine and format results
    const combined = new Map();
    
    [...accountTrends, ...profileTrends].forEach(trend => {
      const dateKey = trend.date.toISOString().split('T')[0];
      if (combined.has(dateKey)) {
        const existing = combined.get(dateKey);
        combined.set(dateKey, {
          label: dateKey,
          value: existing.value + trend.activities,
          users: existing.users + trend.users
        });
      } else {
        combined.set(dateKey, {
          label: dateKey,
          value: trend.activities,
          users: trend.users
        });
      }
    });

    return Array.from(combined.values()).sort((a, b) => a.label.localeCompare(b.label));
  }
  
  private async getUserEngagementTrendChart(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<ChartDataPoint[]> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    const groupBy = this.getGroupByStage(options.granularity);
    
    const engagementData = await ProfileActivityModel.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: groupBy,
          value: { $sum: '$myPtsEarned' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    return engagementData.map(item => ({
      label: this.generatePeriodKey(item._id, options.granularity),
      value: item.value || 0,
      timestamp: new Date(item._id.year, (item._id.month || 1) - 1, item._id.day || 1)
    }));
  }
  
  private async getMyPtsDistributionChart(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<ChartDataPoint[]> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    
    const distribution = await ProfileActivityModel.aggregate([
      { $match: { ...baseQuery, myPtsEarned: { $gt: 0 } } },
      {
        $bucket: {
          groupBy: '$myPtsEarned',
          boundaries: [1, 10, 50, 100, 500, 1000, Number.MAX_VALUE],
          default: 'Other',
          output: { count: { $sum: 1 } }
        }
      }
    ]);

    const labels = ['1-9', '10-49', '50-99', '100-499', '500-999', '1000+'];
    return distribution.map((bucket, index) => ({
      label: labels[index] || 'Other',
      value: bucket.count
    }));
  }
  
  private async getCategoryBreakdownChart(
    options: AnalyticsOptions,
    userId?: mongoose.Types.ObjectId,
    userRole: string = 'user'
  ): Promise<ChartDataPoint[]> {
    const baseQuery = this.buildBaseQuery(options.timeRange, userId, userRole);
    
    const [accountCategories, profileCategories] = await Promise.all([
      AccountActivityModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      ProfileActivityModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ])
    ]);

    const combined = [...accountCategories, ...profileCategories].reduce((acc: any[], cat) => {
      const existing = acc.find(c => c.label === cat._id);
      if (existing) {
        existing.value += cat.count;
      } else {
        acc.push({ label: cat._id, value: cat.count });
      }
      return acc;
    }, []);

    return combined.sort((a, b) => b.value - a.value);
  }

  // Additional helper methods with proper implementations
  private async getTopPointEarners(timeRange: TimeRange, limit: number = 10): Promise<any[]> {
    const topEarners = await ProfileActivityModel.aggregate([
      {
        $match: {
          timestamp: { $gte: timeRange.start, $lte: timeRange.end },
          myPtsEarned: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: '$userId',
          points: { $sum: '$myPtsEarned' },
          activities: { $sum: 1 }
        }
      },
      { $sort: { points: -1 } },
      { $limit: limit }
    ]);

    return topEarners.map((earner, index) => ({
      userId: earner._id.toString(),
      points: earner.points,
      rank: index + 1
    }));
  }
  
  private async getRewardDistribution(timeRange: TimeRange): Promise<any[]> {
    return await ProfileActivityModel.aggregate([
      {
        $match: {
          timestamp: { $gte: timeRange.start, $lte: timeRange.end },
          myPtsEarned: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: '$activityType',
          totalPoints: { $sum: '$myPtsEarned' },
          count: { $sum: 1 }
        }
      },
      { $sort: { totalPoints: -1 } }
    ]).then(results => results.map(r => ({
      activityType: r._id,
      totalPoints: r.totalPoints,
      count: r.count
    })));
  }
  
  private async getPointsFlow(timeRange: TimeRange, granularity: string): Promise<any[]> {
    const groupBy = this.getGroupByStage(granularity);
    
    return await ProfileActivityModel.aggregate([
      {
        $match: {
          timestamp: { $gte: timeRange.start, $lte: timeRange.end },
          $or: [{ myPtsEarned: { $gt: 0 } }, { myPtsSpent: { $gt: 0 } }]
        }
      },
      {
        $group: {
          _id: groupBy,
          earned: { $sum: '$myPtsEarned' },
          spent: { $sum: '$myPtsSpent' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]).then(results => results.map(r => ({
      period: this.generatePeriodKey(r._id, granularity),
      earned: r.earned,
      spent: r.spent,
      net: r.earned - r.spent
    })));
  }
  
  private async getDailyEngagementTrend(timeRange: TimeRange, baseQuery: any): Promise<ChartDataPoint[]> {
    const dailyData = await ProfileActivityModel.aggregate([
      { $match: { ...baseQuery, timestamp: { $gte: timeRange.start, $lte: timeRange.end } } },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          value: { $sum: '$myPtsEarned' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    return dailyData.map(item => ({
      label: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
      value: item.value,
      timestamp: new Date(item._id.year, item._id.month - 1, item._id.day)
    }));
  }
  
  private async generateFeatureUsageAnalysis(timeRange: TimeRange, baseQuery: any): Promise<ChartDataPoint[]> {
    const featureData = await AccountActivityModel.aggregate([
      { $match: { ...baseQuery, timestamp: { $gte: timeRange.start, $lte: timeRange.end } } },
      { $unwind: { path: '$metadata.feature', preserveNullAndEmptyArrays: true } },
      { $group: { _id: '$metadata.feature', value: { $sum: 1 } } },
      { $match: { _id: { $ne: null } } },
      { $sort: { value: -1 } },
      { $limit: 10 }
    ]);

    return featureData.map(item => ({
      label: item._id,
      value: item.value
    }));
  }
  
  private async generateUserJourneyAnalysis(timeRange: TimeRange, baseQuery: any): Promise<any[]> {
    // Simplified user journey analysis
    const journeySteps = ['registration', 'first_login', 'profile_created', 'profile_completed', 'first_connection'];
    const results = [];
    
    for (let i = 0; i < journeySteps.length; i++) {
      const stepUsers = await AccountActivityModel.distinct('userId', {
        ...baseQuery,
        activityType: journeySteps[i],
        timestamp: { $gte: timeRange.start, $lte: timeRange.end }
      });
      
      const previousStepUsers = i > 0 ? await AccountActivityModel.distinct('userId', {
        ...baseQuery,
        activityType: journeySteps[i - 1],
        timestamp: { $gte: timeRange.start, $lte: timeRange.end }
      }) : stepUsers;
      
      results.push({
        step: journeySteps[i],
        users: stepUsers.length,
        dropoffRate: previousStepUsers.length > 0 ? 
          ((previousStepUsers.length - stepUsers.length) / previousStepUsers.length) * 100 : 0
      });
    }
    
    return results;
  }
  
  private async generateSegmentAnalysis(timeRange: TimeRange, baseQuery: any): Promise<any[]> {
    const segments = ['superadmin', 'admin', 'user'];
    const results = [];
    
    for (const segment of segments) {
      const segmentQuery = { ...baseQuery, userRole: segment };
      const metrics = await this.generateUserEngagementMetrics({
        timeRange,
        granularity: 'day'
      }, undefined, segment);
      
      results.push({
        segment,
        metrics
      });
    }
    
    return results;
  }
  
  private async generateMyPtsEarningTrends(timeRange: TimeRange, userId?: mongoose.Types.ObjectId): Promise<ChartDataPoint[]> {
    const query: any = {
      timestamp: { $gte: timeRange.start, $lte: timeRange.end },
      myPtsEarned: { $gt: 0 }
    };
    
    if (userId) query.userId = userId;
    
    const trendData = await ProfileActivityModel.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          value: { $sum: '$myPtsEarned' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    return trendData.map(item => ({
      label: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
      value: item.value,
      timestamp: new Date(item._id.year, item._id.month - 1, item._id.day)
    }));
  }
  
  private async generateMyPtsSpendingTrends(timeRange: TimeRange, userId?: mongoose.Types.ObjectId): Promise<ChartDataPoint[]> {
    const query: any = {
      timestamp: { $gte: timeRange.start, $lte: timeRange.end },
      myPtsSpent: { $gt: 0 }
    };
    
    if (userId) query.userId = userId;
    
    const trendData = await ProfileActivityModel.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          value: { $sum: '$myPtsSpent' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    return trendData.map(item => ({
      label: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
      value: item.value,
      timestamp: new Date(item._id.year, item._id.month - 1, item._id.day)
    }));
  }
  
  private async generateRewardEffectiveness(timeRange: TimeRange): Promise<any[]> {
    return await ProfileActivityModel.aggregate([
      {
        $match: {
          timestamp: { $gte: timeRange.start, $lte: timeRange.end },
          isRewardable: true,
          myPtsEarned: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: '$activityType',
          totalActivities: { $sum: 1 },
          totalPoints: { $sum: '$myPtsEarned' },
          avgPoints: { $avg: '$myPtsEarned' }
        }
      },
      {
        $project: {
          activityType: '$_id',
          conversionRate: { $multiply: [{ $divide: ['$totalActivities', '$totalActivities'] }, 100] },
          avgPoints: '$avgPoints'
        }
      },
      { $sort: { avgPoints: -1 } }
    ]);
  }
  
  private async generateUserLeaderboard(timeRange: TimeRange, limit: number = 100): Promise<any[]> {
    return await ProfileActivityModel.aggregate([
      {
        $match: {
          timestamp: { $gte: timeRange.start, $lte: timeRange.end },
          myPtsEarned: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: '$userId',
          totalPoints: { $sum: '$myPtsEarned' }
        }
      },
      { $sort: { totalPoints: -1 } },
      { $limit: limit }
    ]).then(results => results.map((user, index) => ({
      userId: user._id.toString(),
      username: `User ${user._id.toString().slice(-6)}`, // Simplified username
      totalPoints: user.totalPoints,
      rank: index + 1
    })));
  }
  
  private async getLiveActivityCount(since: Date): Promise<number> {
    const [accountCount, profileCount] = await Promise.all([
      AccountActivityModel.countDocuments({ timestamp: { $gte: since } }),
      ProfileActivityModel.countDocuments({ timestamp: { $gte: since } })
    ]);
    
    return accountCount + profileCount;
  }
  
  private async getTopActivityTypes(since: Date): Promise<ChartDataPoint[]> {
    const [accountTypes, profileTypes] = await Promise.all([
      AccountActivityModel.aggregate([
        { $match: { timestamp: { $gte: since } } },
        { $group: { _id: '$activityType', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ]),
      ProfileActivityModel.aggregate([
        { $match: { timestamp: { $gte: since } } },
        { $group: { _id: '$activityType', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ])
    ]);

    const combined = [...accountTypes, ...profileTypes].reduce((acc: any[], type) => {
      const existing = acc.find(t => t.label === type._id);
      if (existing) {
        existing.value += type.count;
      } else {
        acc.push({ label: type._id, value: type.count });
      }
      return acc;
    }, []);

    return combined.sort((a, b) => b.value - a.value).slice(0, 5);
  }
  
  private async getUserDistribution(since: Date): Promise<ChartDataPoint[]> {
    const userRoles = await AccountActivityModel.aggregate([
      { $match: { timestamp: { $gte: since } } },
      { $group: { _id: '$userRole', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    return userRoles.map(role => ({
      label: role._id,
      value: role.count
    }));
  }
  
  private async getPerformanceMetrics(since: Date): Promise<any> {
    // Simplified performance metrics
    const totalActivities = await this.getLiveActivityCount(since);
    const timeWindow = (Date.now() - since.getTime()) / 1000; // seconds
    
    return {
      avgResponseTime: 150, // Mock value
      throughput: totalActivities / timeWindow,
      errorRate: 0.01 // Mock 1% error rate
    };
  }
  
  private assessSystemHealth(performanceMetrics: any, liveActivityCount: number): any {
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (performanceMetrics.avgResponseTime > 1000 || performanceMetrics.errorRate > 0.05) {
      status = 'warning';
    }
    
    if (performanceMetrics.avgResponseTime > 3000 || performanceMetrics.errorRate > 0.1) {
      status = 'critical';
    }
    
    return {
      status,
      metrics: {
        activeConnections: liveActivityCount,
        responseTime: performanceMetrics.avgResponseTime,
        throughput: performanceMetrics.throughput,
        errorRate: performanceMetrics.errorRate
      }
    };
  }
}

// Singleton instance
export const activityAnalyticsService = new ActivityAnalyticsService();