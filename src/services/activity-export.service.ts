/**
 * @file activity-export.service.ts
 * @description Activity Export Service with GDPR Compliance
 * ========================================================
 * 
 * This service handles comprehensive activity export functionality with
 * support for CSV, JSON, and XLSX formats, GDPR compliance features,
 * and advanced filtering capabilities.
 */

import fs from 'fs';
import path from 'path';
import * as csvWriter from 'csv-writer';
import ExcelJS from 'exceljs';
import mongoose from 'mongoose';
import { AccountActivityModel } from '../models/account-activity.model';
import { ProfileActivityModel } from '../models/gamification/user-activity.model';
import { 
  IAccountActivity, 
  IProfileActivity,
  IActivityFilter,
  IActivityExport 
} from '../interfaces/activity-log.interface';
import { logger } from '../utils/logger';
import { RoleType } from '../models/Role';

interface ExportProgress {
  totalRecords: number;
  processedRecords: number;
  currentStep: string;
  estimatedCompletion: Date;
  errors: string[];
}

interface ExportResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  recordsExported: number;
  exportFormat: string;
  exportId: string;
  errors?: string[];
  downloadUrl?: string;
  expiresAt?: Date;
}

export class ActivityExportService {
  private exportDirectory: string;
  private maxExportSize: number;
  private exportRetentionDays: number;
  private activeExports: Map<string, ExportProgress>;

  constructor() {
    this.exportDirectory = process.env.EXPORT_DIRECTORY || './exports';
    this.maxExportSize = parseInt(process.env.MAX_EXPORT_SIZE || '100000'); // 100k records max
    this.exportRetentionDays = parseInt(process.env.EXPORT_RETENTION_DAYS || '7');
    this.activeExports = new Map();

    // Ensure export directory exists
    this.ensureExportDirectory();

    // Cleanup old exports every hour
    setInterval(() => this.cleanupOldExports(), 60 * 60 * 1000);
  }

  // ============================================================================
  // Main Export Methods
  // ============================================================================

  /**
   * Export activities with comprehensive filtering and GDPR compliance
   */
  async exportActivities(
    exportConfig: IActivityExport,
    requestingUserId: mongoose.Types.ObjectId,
    userRole: RoleType,
  ): Promise<ExportResult> {
    const exportId = this.generateExportId();
    
    try {
      // Validate export request
      const validation = await this.validateExportRequest(exportConfig, userRole);
      if (!validation.isValid) {
        return {
          success: false,
          exportFormat: exportConfig.format,
          exportId,
          recordsExported: 0,
          errors: validation.errors
        };
      }

      // Initialize export progress tracking
      this.activeExports.set(exportId, {
        totalRecords: 0,
        processedRecords: 0,
        currentStep: 'Initializing',
        estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes estimate
        errors: []
      });

      // Get activity data based on filters
      const activityData = await this.gatherActivityData(
        exportConfig.filters || {},
        userRole,
        requestingUserId,
        exportId
      );

      if (activityData.length === 0) {
        return {
          success: false,
          exportFormat: exportConfig.format,
          exportId,
          recordsExported: 0,
          errors: ['No activities found matching the specified criteria']
        };
      }

      // Check export size limits
      if (activityData.length > this.maxExportSize) {
        return {
          success: false,
          exportFormat: exportConfig.format,
          exportId,
          recordsExported: 0,
          errors: [`Export size exceeds maximum limit of ${this.maxExportSize} records`]
        };
      }

      // Process data based on GDPR requirements
      const processedData = await this.processDataForExport(
        activityData,
        exportConfig,
        userRole,
        exportId
      );

      // Generate export file
      const exportResult = await this.generateExportFile(
        processedData,
        exportConfig,
        exportId
      );

      // Log export completion
      logger.info('Activity export completed', {
        exportId,
        format: exportConfig.format,
        recordsExported: processedData.length,
        requestingUserId: requestingUserId.toString(),
        userRole
      });

      return {
        success: true,
        ...exportResult,
        exportFormat: exportConfig.format,
        exportId,
        recordsExported: processedData.length,
        downloadUrl: `/api/activity-logs/exports/${exportId}/download`,
        expiresAt: new Date(Date.now() + this.exportRetentionDays * 24 * 60 * 60 * 1000)
      };

    } catch (error) {
      logger.error('Error during activity export:', error);
      
      return {
        success: false,
        exportFormat: exportConfig.format,
        exportId,
        recordsExported: 0,
        errors: [error instanceof Error ? error.message : 'Unknown export error']
      };
    } finally {
      this.activeExports.delete(exportId);
    }
  }

  /**
   * Export user data for GDPR compliance (all user activities)
   */
  async exportUserDataGDPR(
    userId: mongoose.Types.ObjectId,
    format: 'json' | 'csv' | 'xlsx' = 'json',
    includePersonalData: boolean = true
  ): Promise<ExportResult> {
    const exportId = this.generateExportId();
    
    try {
      // Get all user activities
      const [accountActivities, profileActivities] = await Promise.all([
        AccountActivityModel.find({ userId }).lean(),
        ProfileActivityModel.find({ userId }).lean()
      ]);

      const allActivities = [
        ...accountActivities.map((a: any) => ({ ...a, scope: 'account' })),
        ...profileActivities.map((a: any) => ({ ...a, scope: 'profile' }))
      ];

      // Sort by timestamp
      allActivities.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      // Process for GDPR export (include all data if requested)
      const exportConfig: IActivityExport = {
        format,
        includePersonalData,
        includeMetadata: true,
        columns: this.getGDPRExportColumns()
      };

      const processedData = includePersonalData 
        ? allActivities 
        : allActivities.map(this.anonymizeActivityData);

      const exportResult = await this.generateExportFile(
        processedData,
        exportConfig,
        exportId
      );

      logger.info('GDPR export completed', {
        exportId,
        userId: userId.toString(),
        format,
        recordsExported: processedData.length,
        includePersonalData
      });

      return {
        success: true,
        ...exportResult,
        exportFormat: format,
        exportId,
        recordsExported: processedData.length,
        downloadUrl: `/api/activity-logs/exports/${exportId}/download`,
        expiresAt: new Date(Date.now() + this.exportRetentionDays * 24 * 60 * 60 * 1000)
      };

    } catch (error) {
      logger.error('Error during GDPR export:', error);
      return {
        success: false,
        exportFormat: format,
        exportId,
        recordsExported: 0,
        errors: [error instanceof Error ? error.message : 'GDPR export failed']
      };
    }
  }

  /**
   * Get export progress
   */
  getExportProgress(exportId: string): ExportProgress | null {
    return this.activeExports.get(exportId) || null;
  }

  /**
   * Download exported file
   */
  async downloadExport(exportId: string): Promise<{ filePath: string; fileName: string } | null> {
    try {
      const exportPath = path.join(this.exportDirectory, exportId);
      const files = fs.readdirSync(exportPath);
      
      if (files.length === 0) {
        return null;
      }

      const fileName = files[0];
      const filePath = path.join(exportPath, fileName);

      if (!fs.existsSync(filePath)) {
        return null;
      }

      return { filePath, fileName };

    } catch (error) {
      logger.error('Error downloading export:', error);
      return null;
    }
  }

  // ============================================================================
  // Data Gathering and Processing
  // ============================================================================

  private async gatherActivityData(
    filters: IActivityFilter,
    userRole: string,
    requestingUserId: mongoose.Types.ObjectId,
    exportId: string
  ): Promise<any[]> {
    this.updateExportProgress(exportId, 'Gathering activity data', 0);

    try {
      // Build queries based on filters and role permissions
      const accountQuery = this.buildExportQuery(filters, 'account', userRole, requestingUserId);
      const profileQuery = this.buildExportQuery(filters, 'profile', userRole, requestingUserId);

      this.updateExportProgress(exportId, 'Fetching account activities', 25);

      // Fetch account activities
      const accountActivities = await AccountActivityModel
        .find(accountQuery)
        .populate('userId', 'email fullName role')
        .populate('profileId', 'profileInformation.name profileInformation.username')
        .lean();

      this.updateExportProgress(exportId, 'Fetching profile activities', 50);

      // Fetch profile activities
      const profileActivities = await ProfileActivityModel
        .find(profileQuery)
        .populate('userId', 'email fullName role')
        .populate('profileId', 'profileInformation.name profileInformation.username')
        .populate('relatedProfileId', 'profileInformation.name profileInformation.username')
        .lean();

      this.updateExportProgress(exportId, 'Combining activity data', 75);

      // Combine and sort activities
      const allActivities = [
        ...accountActivities.map((a: any) => ({ ...a, scope: 'account' })),
        ...profileActivities.map((a: any) => ({ ...a, scope: 'profile' }))
      ];

      // Sort by timestamp (newest first)
      allActivities.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      this.updateExportProgress(exportId, 'Data gathering complete', 100);

      return allActivities;

    } catch (error) {
      this.updateExportProgress(exportId, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`, 0, [error instanceof Error ? error.message : 'Unknown error']);
      throw error;
    }
  }

  private buildExportQuery(
    filters: IActivityFilter,
    scope: 'account' | 'profile',
    userRole: string,
    requestingUserId: mongoose.Types.ObjectId
  ): any {
    const query: any = {};

    // Apply time range filters
    if (filters.startDate || filters.endDate) {
      query.timestamp = {};
      if (filters.startDate) query.timestamp.$gte = filters.startDate;
      if (filters.endDate) query.timestamp.$lte = filters.endDate;
    }

    // Apply activity type filters
    if (filters.activityTypes && filters.activityTypes.length > 0) {
      query.activityType = { $in: filters.activityTypes };
    }

    // Apply category filters
    if (filters.categories && filters.categories.length > 0) {
      query.category = { $in: filters.categories };
    }

    // Apply role-based access control
    if (userRole === 'user') {
      // Users can only export their own activities
      query.userId = requestingUserId;
    } else if (userRole === 'admin') {
      // Admins can export activities but with some restrictions
      if (filters.userRoles && filters.userRoles.length > 0) {
        query.userRole = { $in: filters.userRoles };
      }
    }
    // Superadmins have full access

    // Apply additional filters
    if (filters.isRewardable !== undefined) {
      query.isRewardable = filters.isRewardable;
    }

    if (filters.isSystemGenerated !== undefined) {
      query.isSystemGenerated = filters.isSystemGenerated;
    }

    // Profile-specific filters
    if (scope === 'profile') {
      if (filters.hasMyPtsEarned !== undefined) {
        query.myPtsEarned = filters.hasMyPtsEarned ? { $gt: 0 } : { $eq: 0 };
      }
      
      if (filters.profileIds && filters.profileIds.length > 0) {
        query.profileId = { $in: filters.profileIds };
      }
    }

    return query;
  }

  private async processDataForExport(
    activityData: any[],
    exportConfig: IActivityExport,
    userRole: string,
    exportId: string
  ): Promise<any[]> {
    this.updateExportProgress(exportId, 'Processing data for export', 0);

    const processedData = activityData.map((activity, index) => {
      this.updateExportProgress(
        exportId, 
        'Processing data for export', 
        Math.round((index / activityData.length) * 100)
      );

      let processedActivity = { ...activity };

      // Remove sensitive data based on role and configuration
      if (userRole !== 'superadmin' || !exportConfig.includePersonalData) {
        processedActivity = this.sanitizeActivityForExport(processedActivity, userRole);
      }

      // Include/exclude metadata based on configuration
      if (!exportConfig.includeMetadata) {
        delete processedActivity.metadata;
      }

      // Flatten nested objects for CSV/Excel export
      if (exportConfig.format !== 'json') {
        processedActivity = this.flattenActivityObject(processedActivity);
      }

      return processedActivity;
    });

    this.updateExportProgress(exportId, 'Data processing complete', 100);
    return processedData;
  }

  // ============================================================================
  // File Generation Methods
  // ============================================================================

  private async generateExportFile(
    data: any[],
    exportConfig: IActivityExport,
    exportId: string
  ): Promise<{ filePath: string; fileName: string; fileSize: number }> {
    const exportPath = path.join(this.exportDirectory, exportId);
    this.ensureDirectoryExists(exportPath);

    const timestamp = new Date().toISOString().split('T')[0];
    const fileName = `activity-export-${timestamp}.${exportConfig.format}`;
    const filePath = path.join(exportPath, fileName);

    switch (exportConfig.format) {
      case 'csv':
        await this.generateCSVFile(data, filePath, exportConfig);
        break;
      case 'xlsx':
        await this.generateExcelFile(data, filePath, exportConfig);
        break;
      case 'json':
      default:
        await this.generateJSONFile(data, filePath, exportConfig);
        break;
    }

    const stats = fs.statSync(filePath);
    return { filePath, fileName, fileSize: stats.size };
  }

  private async generateCSVFile(data: any[], filePath: string, exportConfig: IActivityExport): Promise<void> {
    const columns = exportConfig.columns || this.getDefaultCSVColumns();
    
    const csvWriterInstance = csvWriter.createObjectCsvWriter({
      path: filePath,
      header: columns.map(col => ({ id: col, title: col.toUpperCase() }))
    });

    await csvWriterInstance.writeRecords(data);
  }

  private async generateExcelFile(data: any[], filePath: string, exportConfig: IActivityExport): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Activity Export');

    // Add metadata sheet if requested
    if (exportConfig.includeMetadata) {
      const metadataSheet = workbook.addWorksheet('Export Metadata');
      metadataSheet.addRow(['Export Generated', new Date().toISOString()]);
      metadataSheet.addRow(['Total Records', data.length]);
      metadataSheet.addRow(['Export Format', 'XLSX']);
    }

    // Define columns
    const columns = exportConfig.columns || this.getDefaultExcelColumns();
    worksheet.columns = columns.map(col => ({
      header: col.toUpperCase(),
      key: col,
      width: this.getColumnWidth(col)
    }));

    // Add data rows
    data.forEach(record => {
      worksheet.addRow(record);
    });

    // Style header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    await workbook.xlsx.writeFile(filePath);
  }

  private async generateJSONFile(data: any[], filePath: string, exportConfig: IActivityExport): Promise<void> {
    const exportData = {
      exportMetadata: {
        generatedAt: new Date().toISOString(),
        totalRecords: data.length,
        format: 'JSON',
        includePersonalData: exportConfig.includePersonalData || false,
        includeMetadata: exportConfig.includeMetadata || false
      },
      activities: data
    };

    fs.writeFileSync(filePath, JSON.stringify(exportData, null, 2));
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private validateExportRequest(
    exportConfig: IActivityExport,
    userRole: string
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate format
    if (!['csv', 'json', 'xlsx'].includes(exportConfig.format)) {
      errors.push('Invalid export format. Supported formats: csv, json, xlsx');
    }

    // Validate role permissions for personal data
    if (exportConfig.includePersonalData && userRole === 'user') {
      errors.push('Users cannot export personal data of other users');
    }

    // Validate filters
    if (exportConfig.filters) {
      const filters = exportConfig.filters;
      
      if (filters.startDate && filters.endDate && filters.startDate > filters.endDate) {
        errors.push('Start date cannot be later than end date');
      }

      if (filters.limit && filters.limit > this.maxExportSize) {
        errors.push(`Export limit cannot exceed ${this.maxExportSize} records`);
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  private sanitizeActivityForExport(activity: any, userRole: string): any {
    const sanitized = { ...activity };

    // Remove sensitive fields based on role
    if (userRole !== 'superadmin') {
      delete sanitized.ipAddress;
      delete sanitized.deviceFingerprint;
      delete sanitized.sessionId;
      
      // Sanitize metadata
      if (sanitized.metadata) {
        const { password, token, secret, ...safeMetadata } = sanitized.metadata;
        sanitized.metadata = safeMetadata;
      }
    }

    return sanitized;
  }

  private anonymizeActivityData(activity: any): any {
    const anonymized = { ...activity };
    
    // Remove or anonymize personal identifiers
    delete anonymized.userId;
    delete anonymized.ipAddress;
    delete anonymized.userAgent;
    delete anonymized.deviceFingerprint;
    delete anonymized.sessionId;
    
    // Anonymize metadata
    if (anonymized.metadata) {
      const anonymizedMetadata: any = {};
      Object.keys(anonymized.metadata).forEach(key => {
        if (['activityType', 'category', 'source', 'timestamp'].includes(key)) {
          anonymizedMetadata[key] = anonymized.metadata[key];
        } else {
          anonymizedMetadata[key] = 'anonymized';
        }
      });
      anonymized.metadata = anonymizedMetadata;
    }

    return anonymized;
  }

  private flattenActivityObject(activity: any): any {
    const flattened: any = {};
    
    Object.keys(activity).forEach(key => {
      if (activity[key] && typeof activity[key] === 'object' && !Array.isArray(activity[key])) {
        // Flatten nested objects
        Object.keys(activity[key]).forEach(nestedKey => {
          flattened[`${key}_${nestedKey}`] = activity[key][nestedKey];
        });
      } else {
        flattened[key] = activity[key];
      }
    });

    return flattened;
  }

  private updateExportProgress(
    exportId: string, 
    step: string, 
    progress: number, 
    errors: string[] = []
  ): void {
    const exportProgress = this.activeExports.get(exportId);
    if (exportProgress) {
      exportProgress.currentStep = step;
      exportProgress.processedRecords = progress;
      exportProgress.errors.push(...errors);
      this.activeExports.set(exportId, exportProgress);
    }
  }

  private generateExportId(): string {
    return `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private ensureExportDirectory(): void {
    if (!fs.existsSync(this.exportDirectory)) {
      fs.mkdirSync(this.exportDirectory, { recursive: true });
    }
  }

  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  private cleanupOldExports(): void {
    try {
      const cutoffDate = new Date(Date.now() - this.exportRetentionDays * 24 * 60 * 60 * 1000);
      const exportDirs = fs.readdirSync(this.exportDirectory);

      exportDirs.forEach(dir => {
        const dirPath = path.join(this.exportDirectory, dir);
        const stats = fs.statSync(dirPath);
        
        if (stats.isDirectory() && stats.mtime < cutoffDate) {
          fs.rmSync(dirPath, { recursive: true, force: true });
          logger.info(`Cleaned up old export: ${dir}`);
        }
      });
    } catch (error) {
      logger.error('Error cleaning up old exports:', error);
    }
  }

  private getDefaultCSVColumns(): string[] {
    return [
      'timestamp', 'activityType', 'category', 'scope', 'userRole',
      'isRewardable', 'myPtsEarned', 'myPtsSpent', 'source'
    ];
  }

  private getDefaultExcelColumns(): string[] {
    return [
      'timestamp', 'activityType', 'category', 'scope', 'userRole',
      'isRewardable', 'isBonusable', 'myPtsEarned', 'myPtsSpent', 
      'visibility', 'source', 'createdAt'
    ];
  }

  private getGDPRExportColumns(): string[] {
    return [
      'timestamp', 'activityType', 'category', 'scope', 'metadata',
      'ipAddress', 'userAgent', 'sessionId', 'source', 'createdAt'
    ];
  }

  private getColumnWidth(columnName: string): number {
    const widthMap: Record<string, number> = {
      timestamp: 20,
      activityType: 25,
      category: 20,
      metadata: 30,
      ipAddress: 15,
      userAgent: 30,
      default: 15
    };

    return widthMap[columnName] || widthMap.default;
  }
}

// Singleton instance
export const activityExportService = new ActivityExportService();