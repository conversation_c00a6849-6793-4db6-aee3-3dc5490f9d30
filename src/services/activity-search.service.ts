/**
 * @file activity-search.service.ts
 * @description Advanced Activity Search and Filtering Service
 * =======================================================
 * 
 * This service provides comprehensive search and filtering capabilities
 * for activity logs with full-text search, advanced filtering, and
 * performance optimization for large datasets.
 */

import mongoose from 'mongoose';
import { AccountActivityModel } from '../models/account-activity.model';
import { ProfileActivityModel } from '../models/gamification/user-activity.model';
import { 
  IAccountActivity, 
  IProfileActivity,
  IActivityFilter,
  ActivityVisibility 
} from '../interfaces/activity-log.interface';
import { ActivityType } from '../constants/activity-types';
import { logger } from '../utils/logger';
import { RoleType } from '../models/Role';

interface SearchOptions {
  textSearch?: string;
  fuzzySearch?: boolean;
  searchFields?: string[];
  caseSensitive?: boolean;
  exactMatch?: boolean;
}

interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface SearchResult<T> {
  data: T[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
  searchScore?: number;
}

interface ActivitySearchContext {
  userId: mongoose.Types.ObjectId;
  userRole: RoleType;
  profileIds?: mongoose.Types.ObjectId[];
  allowedVisibility: ActivityVisibility[];
}

interface AggregatedSearchResult {
  activities: (IAccountActivity | IProfileActivity)[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
  facets: {
    activityTypes: Array<{ type: string; count: number }>;
    categories: Array<{ category: string; count: number }>;
    timeDistribution: Array<{ period: string; count: number }>;
    userRoles: Array<{ role: string; count: number }>;
  };
  searchMetrics: {
    searchTime: number;
    indexesUsed: string[];
    totalScanned: number;
    totalReturned: number;
  };
}

export class ActivitySearchService {
  private searchIndexes: Map<string, any>;

  constructor() {
    this.searchIndexes = new Map();
    this.initializeSearchIndexes();
  }

  // ============================================================================
  // Main Search Methods
  // ============================================================================

  /**
   * Comprehensive search across all activities with advanced filtering
   */
  async searchActivities(
    filters: IActivityFilter,
    searchOptions: SearchOptions,
    paginationOptions: PaginationOptions,
    context: ActivitySearchContext
  ): Promise<AggregatedSearchResult> {
    const startTime = Date.now();

    try {
      // Build search queries for both models
      const accountQuery = this.buildSearchQuery(filters, searchOptions, context, 'account');
      const profileQuery = this.buildSearchQuery(filters, searchOptions, context, 'profile');

      // Build aggregation pipelines
      const accountPipeline = this.buildAggregationPipeline(accountQuery, searchOptions, paginationOptions);
      const profilePipeline = this.buildAggregationPipeline(profileQuery, searchOptions, paginationOptions);

      // Execute searches in parallel
      const [accountResults, profileResults] = await Promise.all([
        AccountActivityModel.aggregate(accountPipeline),
        ProfileActivityModel.aggregate(profilePipeline)
      ]);

      // Combine and process results
      const combinedResults = this.combineSearchResults(
        accountResults,
        profileResults,
        paginationOptions
      );

      // Generate facets and metrics
      const facets = await this.generateSearchFacets(filters, context);
      const searchMetrics = {
        searchTime: Date.now() - startTime,
        indexesUsed: this.getUsedIndexes(filters, searchOptions),
        totalScanned: combinedResults.totalCount,
        totalReturned: combinedResults.activities.length
      };

      return {
        ...combinedResults,
        facets,
        searchMetrics
      };

    } catch (error) {
      logger.error('Error in comprehensive activity search:', error);
      throw new Error('Activity search failed');
    }
  }

  /**
   * Quick search for real-time suggestions
   */
  async quickSearch(
    searchTerm: string,
    context: ActivitySearchContext,
    limit: number = 10
  ): Promise<SearchResult<any>> {
    try {
      const searchOptions: SearchOptions = {
        textSearch: searchTerm,
        fuzzySearch: true,
        searchFields: ['activityType', 'category', 'metadata.description']
      };

      const paginationOptions: PaginationOptions = {
        page: 1,
        limit,
        sortBy: 'timestamp',
        sortOrder: 'desc'
      };

      const filters: IActivityFilter = {
        // Apply basic visibility filters based on context
        visibility: context.allowedVisibility
      };

      const results = await this.searchActivities(filters, searchOptions, paginationOptions, context);

      return {
        data: results.activities,
        totalCount: results.totalCount,
        hasMore: results.hasMore,
        currentPage: results.currentPage,
        totalPages: results.totalPages
      };

    } catch (error) {
      logger.error('Error in quick search:', error);
      throw new Error('Quick search failed');
    }
  }

  /**
   * Search similar activities based on activity pattern
   */
  async findSimilarActivities(
    activityId: string,
    context: ActivitySearchContext,
    limit: number = 20
  ): Promise<SearchResult<any>> {
    try {
      // Get the reference activity
      const referenceActivity = await this.getActivityById(activityId, context);
      if (!referenceActivity) {
        throw new Error('Reference activity not found');
      }

      // Build similarity query
      const similarityFilters: IActivityFilter = {
        activityTypes: [referenceActivity.activityType],
        categories: [referenceActivity.category],
        excludeIds: [activityId]
      };

      // Use metadata similarity for more precise matching
      if (referenceActivity.metadata) {
        similarityFilters.metadataKeys = Object.keys(referenceActivity.metadata);
      }

      const searchOptions: SearchOptions = {
        searchFields: ['activityType', 'category', 'metadata'],
        exactMatch: false
      };

      const paginationOptions: PaginationOptions = {
        page: 1,
        limit,
        sortBy: 'timestamp',
        sortOrder: 'desc'
      };

      const results = await this.searchActivities(similarityFilters, searchOptions, paginationOptions, context);

      return {
        data: results.activities,
        totalCount: results.totalCount,
        hasMore: results.hasMore,
        currentPage: results.currentPage,
        totalPages: results.totalPages
      };

    } catch (error) {
      logger.error('Error finding similar activities:', error);
      throw new Error('Similar activities search failed');
    }
  }

  /**
   * Advanced filtering with custom criteria
   */
  async advancedFilter(
    customFilters: {
      dateRange?: { start: Date; end: Date };
      timeOfDay?: { start: string; end: string }; // HH:mm format
      dayOfWeek?: number[]; // 0-6, Sunday = 0
      ipAddressPattern?: string;
      userAgentPattern?: string;
      metadataContains?: Record<string, any>;
      myPtsRange?: { min: number; max: number };
      sessionDuration?: { min: number; max: number };
      geolocation?: { 
        center: { lat: number; lng: number }; 
        radius: number; // in kilometers
      };
    },
    context: ActivitySearchContext,
    paginationOptions: PaginationOptions = {}
  ): Promise<SearchResult<any>> {
    try {
      const query = this.buildAdvancedFilterQuery(customFilters, context);

      // Execute query with pagination
      const pipeline = [
        { $match: query },
        { $sort: { [paginationOptions.sortBy || 'timestamp']: paginationOptions.sortOrder === 'asc' ? 1 as const : -1 as const } },
        {
          $facet: {
            data: [
              { $skip: ((paginationOptions.page || 1) - 1) * (paginationOptions.limit || 20) },
              { $limit: paginationOptions.limit || 20 }
            ],
            totalCount: [{ $count: 'count' }]
          }
        }
      ];

      const [accountResults, profileResults] = await Promise.all([
        AccountActivityModel.aggregate(pipeline),
        ProfileActivityModel.aggregate(pipeline)
      ]);

      // Combine results
      const combinedData = [
        ...accountResults[0]?.data?.map((a: any) => ({ ...a, scope: 'account' })) || [],
        ...profileResults[0]?.data?.map((p: any) => ({ ...p, scope: 'profile' })) || []
      ];

      const totalCount = (accountResults[0]?.totalCount[0]?.count || 0) + 
                        (profileResults[0]?.totalCount[0]?.count || 0);

      // Sort combined results
      combinedData.sort((a, b) => {
        const aValue = a[paginationOptions.sortBy || 'timestamp'];
        const bValue = b[paginationOptions.sortBy || 'timestamp'];
        const sortDirection = paginationOptions.sortOrder === 'asc' ? 1 : -1;
        
        if (aValue < bValue) return -1 * sortDirection;
        if (aValue > bValue) return 1 * sortDirection;
        return 0;
      });

      const limit = paginationOptions.limit || 20;
      const currentPage = paginationOptions.page || 1;
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: combinedData.slice(0, limit),
        totalCount,
        hasMore: currentPage < totalPages,
        currentPage,
        totalPages
      };

    } catch (error) {
      logger.error('Error in advanced filtering:', error);
      throw new Error('Advanced filtering failed');
    }
  }

  // ============================================================================
  // Query Building Methods
  // ============================================================================

  private buildSearchQuery(
    filters: IActivityFilter,
    searchOptions: SearchOptions,
    context: ActivitySearchContext,
    scope: 'account' | 'profile'
  ): any {
    const query: any = {};

    // Apply role-based access control
    this.applyAccessControl(query, context, scope);

    // Apply time range filters
    if (filters.startDate || filters.endDate) {
      query.timestamp = {};
      if (filters.startDate) query.timestamp.$gte = filters.startDate;
      if (filters.endDate) query.timestamp.$lte = filters.endDate;
    }

    // Apply activity type filters
    if (filters.activityTypes && filters.activityTypes.length > 0) {
      query.activityType = { $in: filters.activityTypes };
    }

    // Apply category filters
    if (filters.categories && filters.categories.length > 0) {
      query.category = { $in: filters.categories };
    }

    // Apply text search
    if (searchOptions.textSearch) {
      query.$text = { 
        $search: searchOptions.textSearch,
        $caseSensitive: searchOptions.caseSensitive || false
      };
    }

    // Apply user role filters
    if (filters.userRoles && filters.userRoles.length > 0) {
      query.userRole = { $in: filters.userRoles };
    }

    // Apply visibility filters (profile-specific)
    if (scope === 'profile' && filters.visibility) {
      query.visibility = { $in: Array.isArray(filters.visibility) ? filters.visibility : [filters.visibility] };
    }

    // Apply reward filters
    if (filters.isRewardable !== undefined) {
      query.isRewardable = filters.isRewardable;
    }

    if (filters.isBonusable !== undefined) {
      query.isBonusable = filters.isBonusable;
    }

    // Apply MyPts filters (profile-specific)
    if (scope === 'profile') {
      if (filters.hasMyPtsEarned !== undefined) {
        query.myPtsEarned = filters.hasMyPtsEarned ? { $gt: 0 } : { $eq: 0 };
      }

      if (filters.myPtsRange) {
        query.myPtsEarned = {
          $gte: filters.myPtsRange.min || 0,
          $lte: filters.myPtsRange.max || Number.MAX_SAFE_INTEGER
        };
      }
    }

    // Apply system activity filter
    if (filters.isSystemGenerated !== undefined) {
      query.isSystemGenerated = filters.isSystemGenerated;
    }

    // Apply exclude IDs filter
    if (filters.excludeIds && filters.excludeIds.length > 0) {
      query._id = { $nin: filters.excludeIds.map(id => new mongoose.Types.ObjectId(id)) };
    }

    // Apply profile-specific filters
    if (filters.profileIds && filters.profileIds.length > 0) {
      query.profileId = { $in: filters.profileIds };
    }

    // Apply metadata filters
    if (filters.metadataKeys && filters.metadataKeys.length > 0) {
      const metadataConditions = filters.metadataKeys.map(key => ({
        [`metadata.${key}`]: { $exists: true }
      }));
      query.$or = metadataConditions;
    }

    return query;
  }

  private buildAdvancedFilterQuery(
    customFilters: any,
    context: ActivitySearchContext
  ): any {
    const query: any = {};

    // Apply access control
    this.applyAccessControl(query, context, 'both');

    // Date range filter
    if (customFilters.dateRange) {
      query.timestamp = {
        $gte: customFilters.dateRange.start,
        $lte: customFilters.dateRange.end
      };
    }

    // Time of day filter
    if (customFilters.timeOfDay) {
      const startHour = parseInt(customFilters.timeOfDay.start.split(':')[0]);
      const endHour = parseInt(customFilters.timeOfDay.end.split(':')[0]);
      
      query.$expr = {
        $and: [
          { $gte: [{ $hour: '$timestamp' }, startHour] },
          { $lte: [{ $hour: '$timestamp' }, endHour] }
        ]
      };
    }

    // Day of week filter
    if (customFilters.dayOfWeek && customFilters.dayOfWeek.length > 0) {
      query.$expr = {
        ...(query.$expr || {}),
        $in: [{ $dayOfWeek: '$timestamp' }, customFilters.dayOfWeek]
      };
    }

    // IP address pattern
    if (customFilters.ipAddressPattern) {
      query.ipAddress = { $regex: customFilters.ipAddressPattern, $options: 'i' };
    }

    // User agent pattern
    if (customFilters.userAgentPattern) {
      query.userAgent = { $regex: customFilters.userAgentPattern, $options: 'i' };
    }

    // Metadata contains
    if (customFilters.metadataContains) {
      Object.keys(customFilters.metadataContains).forEach(key => {
        query[`metadata.${key}`] = customFilters.metadataContains[key];
      });
    }

    // MyPts range filter
    if (customFilters.myPtsRange) {
      query.myPtsEarned = {
        $gte: customFilters.myPtsRange.min,
        $lte: customFilters.myPtsRange.max
      };
    }

    // Geolocation filter (if metadata contains location data)
    if (customFilters.geolocation) {
      const { center, radius } = customFilters.geolocation;
      query['metadata.location'] = {
        $geoWithin: {
          $centerSphere: [[center.lng, center.lat], radius / 6378.1]
        }
      };
    }

    return query;
  }

  private buildAggregationPipeline(
    matchQuery: any,
    searchOptions: SearchOptions,
    paginationOptions: PaginationOptions
  ): any[] {
    const pipeline: any[] = [];

    // Match stage
    pipeline.push({ $match: matchQuery });

    // Text search scoring (if applicable)
    if (searchOptions.textSearch) {
      pipeline.push({
        $addFields: {
          searchScore: { $meta: 'textScore' }
        }
      });
    }

    // Sort stage
    const sortStage: any = {};
    if (searchOptions.textSearch) {
      sortStage.searchScore = { $meta: 'textScore' };
    }
    sortStage[paginationOptions.sortBy || 'timestamp'] = paginationOptions.sortOrder === 'asc' ? 1 as const : -1 as const;
    pipeline.push({ $sort: sortStage });

    // Facet stage for pagination and counting
    pipeline.push({
      $facet: {
        data: [
          { $skip: ((paginationOptions.page || 1) - 1) * (paginationOptions.limit || 20) },
          { $limit: paginationOptions.limit || 20 }
        ],
        totalCount: [{ $count: 'count' }]
      }
    });

    return pipeline;
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private applyAccessControl(
    query: any,
    context: ActivitySearchContext,
    scope: 'account' | 'profile' | 'both'
  ): void {
    // Apply role-based filtering
    if (context.userRole === RoleType.REGULAR_USER) {
      // Users can only see their own activities
      query.userId = context.userId;
      
      // Additional visibility restrictions for profile activities
      if (scope === 'profile' || scope === 'both') {
        query.$or = [
          { visibility: ActivityVisibility.PUBLIC },
          { visibility: ActivityVisibility.CONNECTIONS_ONLY },
          { visibility: ActivityVisibility.PRIVATE, userId: context.userId }
        ];
      }
    } else if (context.userRole === RoleType.ADMIN_USER) {
      // Admins can see most activities but not superadmin-only
      if (scope === 'profile' || scope === 'both') {
        query.visibility = { $ne: ActivityVisibility.ADMIN_ONLY };
      }
    }
    // Superadmins can see everything, no additional restrictions
  } 

  private combineSearchResults(
    accountResults: any[],
    profileResults: any[],
    paginationOptions: PaginationOptions
  ): {
    activities: any[];
    totalCount: number;
    hasMore: boolean;
    currentPage: number;
    totalPages: number;
  } {
    const accountData = accountResults[0]?.data?.map((a: any) => ({ ...a, scope: 'account' })) || [];
    const profileData = profileResults[0]?.data?.map((p: any) => ({ ...p, scope: 'profile' })) || [];

    const accountCount = accountResults[0]?.totalCount[0]?.count || 0;
    const profileCount = profileResults[0]?.totalCount[0]?.count || 0;
    const totalCount = accountCount + profileCount;

    // Combine and sort activities
    const combinedActivities = [...accountData, ...profileData];
    combinedActivities.sort((a, b) => {
      const aValue = a[paginationOptions.sortBy || 'timestamp'];
      const bValue = b[paginationOptions.sortBy || 'timestamp'];
      const sortDirection = paginationOptions.sortOrder === 'asc' ? 1 : -1;
      
      if (aValue < bValue) return -1 * sortDirection;
      if (aValue > bValue) return 1 * sortDirection;
      return 0;
    });

    const limit = paginationOptions.limit || 20;
    const currentPage = paginationOptions.page || 1;
    const totalPages = Math.ceil(totalCount / limit);

    return {
      activities: combinedActivities.slice(0, limit),
      totalCount,
      hasMore: currentPage < totalPages,
      currentPage,
      totalPages
    };
  }

  private async generateSearchFacets(
    filters: IActivityFilter,
    context: ActivitySearchContext
  ): Promise<any> {
    try {
      const baseQuery: any = {};
      this.applyAccessControl(baseQuery, context, 'both');

      // Get facets from both models
      const [accountFacets, profileFacets] = await Promise.all([
        AccountActivityModel.aggregate([
          { $match: baseQuery },
          {
            $facet: {
              activityTypes: [
                { $group: { _id: '$activityType', count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 20 }
              ],
              categories: [
                { $group: { _id: '$category', count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 10 }
              ],
              userRoles: [
                { $group: { _id: '$userRole', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
              ]
            }
          }
        ]),
        ProfileActivityModel.aggregate([
          { $match: baseQuery },
          {
            $facet: {
              activityTypes: [
                { $group: { _id: '$activityType', count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 20 }
              ],
              categories: [
                { $group: { _id: '$category', count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 10 }
              ],
              userRoles: [
                { $group: { _id: '$userRole', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
              ]
            }
          }
        ])
      ]);

      // Combine facets
      const combinedFacets = {
        activityTypes: this.combineFacetResults(
          accountFacets[0]?.activityTypes || [],
          profileFacets[0]?.activityTypes || []
        ),
        categories: this.combineFacetResults(
          accountFacets[0]?.categories || [],
          profileFacets[0]?.categories || []
        ),
        userRoles: this.combineFacetResults(
          accountFacets[0]?.userRoles || [],
          profileFacets[0]?.userRoles || []
        ),
        timeDistribution: await this.generateTimeDistribution(baseQuery)
      };

      return combinedFacets;

    } catch (error) {
      logger.error('Error generating search facets:', error);
      return {
        activityTypes: [],
        categories: [],
        userRoles: [],
        timeDistribution: []
      };
    }
  }

  private combineFacetResults(facet1: any[], facet2: any[]): Array<{ type: string; count: number }> {
    const combined = new Map<string, number>();

    [...facet1, ...facet2].forEach((item) => {
      const key = item._id;
      const count = item.count;
      combined.set(key, (combined.get(key) || 0) + count);
    });

    return Array.from(combined.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);
  }

  private async generateTimeDistribution(baseQuery: any): Promise<Array<{ period: string; count: number }>> {
    try {
      const timeDistribution = await AccountActivityModel.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: {
              year: { $year: '$timestamp' },
              month: { $month: '$timestamp' },
              day: { $dayOfMonth: '$timestamp' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': -1, '_id.month': -1, '_id.day': -1 } },
        { $limit: 30 }
      ]);

      return timeDistribution.map((item) => ({
        period: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
        count: item.count
      }));

    } catch (error) {
      logger.error('Error generating time distribution:', error);
      return [];
    }
  }

  private async getActivityById(
    activityId: string,
    context: ActivitySearchContext
  ): Promise<any | null> {
    try {
      const objectId = new mongoose.Types.ObjectId(activityId);
      
      // Try account activities first
      const accountActivity = await AccountActivityModel.findById(objectId).lean();
      if (accountActivity) {
        return { ...accountActivity, scope: 'account' };
      }

      // Try profile activities
      const profileActivity = await ProfileActivityModel.findById(objectId).lean();
      if (profileActivity) {
        return { ...profileActivity, scope: 'profile' };
      }

      return null;

    } catch (error) {
      logger.error('Error getting activity by ID:', error);
      return null;
    }
  }

  private getUsedIndexes(filters: IActivityFilter, searchOptions: SearchOptions): string[] {
    const indexes: string[] = [];

    if (searchOptions.textSearch) {
      indexes.push('text_index');
    }

    if (filters.startDate || filters.endDate) {
      indexes.push('timestamp_index');
    }

    if (filters.activityTypes) {
      indexes.push('activityType_index');
    }

    if (filters.categories) {
      indexes.push('category_index');
    }

    if (filters.userRoles) {
      indexes.push('userRole_index');
    }

    return indexes;
  }

  private initializeSearchIndexes(): void {
    // This would be called during service initialization to set up
    // any in-memory indexes or search optimization structures
    logger.info('Activity search service initialized with search indexes');
  }

  /**
   * Get search suggestions based on partial input
   */
  async getSearchSuggestions(
    partialInput: string,
    context: ActivitySearchContext,
    limit: number = 5
  ): Promise<string[]> {
    try {
      const suggestions = new Set<string>();

      // Get activity type suggestions
      const activityTypes = await AccountActivityModel.distinct('activityType', {
        activityType: { $regex: partialInput, $options: 'i' }
      });
      activityTypes.slice(0, limit).forEach(type => suggestions.add(type));

      // Get category suggestions
      const categories = await ProfileActivityModel.distinct('category', {
        category: { $regex: partialInput, $options: 'i' }
      });
      categories.slice(0, limit - suggestions.size).forEach(cat => suggestions.add(cat));

      return Array.from(suggestions).slice(0, limit);

    } catch (error) {
      logger.error('Error getting search suggestions:', error);
      return [];
    }
  }
}

// Singleton instance
export const activitySearchService = new ActivitySearchService();