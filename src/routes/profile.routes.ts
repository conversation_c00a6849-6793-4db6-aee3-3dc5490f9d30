import express from 'express';
import { authenticateToken } from '../middleware/authMiddleware';
import { requireRole } from '../middleware/roleMiddleware';
import { RBACMiddleware } from '../middleware/rbac.middleware';
import { ProfileController } from '../controllers/profile.controller';
import {
  createTemplate,
  bulkCreateTemplates,
  listTemplates,
  getTemplateById,
  updateTemplate,
  deleteTemplate
} from '../controllers/admin-profile-template.controller';
import { param } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation.middleware';
import { RoleType } from 'src/models/Role';

const router = express.Router();

// Initialize ProfileController
const profileController = new ProfileController();

// Apply authentication middleware to all routes
// router.use(authenticateToken);

// Admin routes for managing profile templates
router.post('/t/create', authenticateToken, requireRole(['user', 'admin', 'superadmin']), createTemplate);
router.post('/t/bulk-create', authenticateToken, requireRole(['user', 'admin', 'superadmin']), bulkCreateTemplates);
router.get('/t/list', authenticateToken, listTemplates);
router.get('/t/:id', authenticateToken, getTemplateById);
router.put('/t/:id', authenticateToken, requireRole(['user','admin', 'superadmin']), updateTemplate);
router.delete('/t/:id', authenticateToken, requireRole(['user','admin', 'superadmin']), deleteTemplate);

// Template-based profile routes
router.post('/p', authenticateToken, requireRole(['user', 'admin', 'superadmin']), profileController.createProfile.bind(profileController));
router.post('/p/:profileId/fields', authenticateToken, profileController.setEnabledFields.bind(profileController));
router.put('/p/:profileId/content', authenticateToken, profileController.updateProfileContent.bind(profileController));
router.put('/p/:profileId/basic-info', authenticateToken, profileController.updateProfileBasicInfo.bind(profileController));

// Link management routes
router.put('/p/:profileId/links', authenticateToken, profileController.updateProfileLinks.bind(profileController));
router.put('/p/:profileId/links/:linkName/toggle', authenticateToken, profileController.toggleProfileLink.bind(profileController));
router.post('/p/:profileId/links/sync', authenticateToken, profileController.syncProfileLinks.bind(profileController));

router.get('/p/:profileId', profileController.getProfile.bind(profileController)); // Public access
router.get('/view/profile/:username', profileController.getProfileByUsername.bind(profileController)); // Public access by username
router.get('/p/:profileId/structure', profileController.getProfileStructure.bind(profileController)); // Debug endpoint
router.post('/p/:profileId/sync', profileController.syncProfileWithTemplate.bind(profileController)); // Sync with template
router.post('/p/:profileId/populate-basic', authenticateToken, profileController.populateBasicInformation.bind(profileController)); // Populate basic info
router.post('/p/sync-all', authenticateToken, requireRole(['user', 'admin', 'superadmin']), profileController.syncAllPersonalProfiles.bind(profileController)); // Bulk sync all personal profiles
router.post('/p/sync-by-template/:templateId', authenticateToken, requireRole(['admin', 'superadmin']), profileController.syncProfilesByTemplate.bind(profileController));

router.get('/p', authenticateToken, profileController.getUserProfiles.bind(profileController));
router.delete('/p/:profileId', authenticateToken, requireRole(['user', 'admin', 'superadmin']), profileController.deleteProfile.bind(profileController));
router.post('/default', authenticateToken, requireRole(['user', 'admin', 'superadmin']), profileController.createDefaultProfile.bind(profileController));

// Profile publication routes with proper RBAC
router.patch('/p/:profileId/publish', authenticateToken, profileController.publishProfile.bind(profileController));
router.patch('/p/:profileId/unpublish', authenticateToken, profileController.unpublishProfile.bind(profileController));
router.get('/p/:profileId/publication-status', authenticateToken, profileController.getProfilePublicationStatus.bind(profileController));

// Public routes for published profiles
router.get('/published', profileController.getPublishedProfiles.bind(profileController));
router.get('/publication-stats', authenticateToken, RBACMiddleware.requirePermission('profile.publication.stats.view'), profileController.getPublicationStats.bind(profileController));

// Admin routes for managing all profiles
router.get('/all', authenticateToken, profileController.getAllProfiles.bind(profileController));

/**
 * @route DELETE /api/profiles/duplicates/personal
 * @desc Delete duplicate personal profiles, keeping ones with non-zero MYPTS balance
 * @access Admin only
 * @returns {Object} Summary of deletion results including profiles deleted and kept
 */
router.delete('/duplicates/personal', authenticateToken, requireRole([RoleType.]), profileController.deleteDuplicatePersonalProfiles.bind(profileController));

// Get community profiles with filters
router.get(
  '/communities',
  profileController.getCommunityProfiles.bind(profileController)
);

export default router;
