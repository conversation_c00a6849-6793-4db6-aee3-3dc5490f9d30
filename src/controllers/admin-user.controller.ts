import { Request, Response } from 'express';
import adminUserService, { UserFilters } from '../services/admin-user.service';
import { logger } from '../utils/logger';
import { User } from '../models/User';
import { RoleType, RoleLevel } from '../models/Role';
import { RBACService } from '../services/rbac.service';

/**
 * Admin User Management Controller
 * Handles all user management operations for admins and super admins
 */
export class AdminUserController {
  /**
   * Create a new user
   * @route POST /api/admin/users
   */
  static async createUser(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user) {
        return res.status(403).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // Check if user has permission to create users
      const hasPermission = await RBACService.hasPermission(user._id, 'user.create');
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions to create users'
        });
      }

      // Validate required fields
      const {
        email,
        password,
        fullName,
        username,
        firstName,
        lastName,
        dateOfBirth,
        countryOfResidence,
        phoneNumber,
        accountType,
        accountCategory,
        verificationMethod,
        roleType, // Changed from 'role' to 'roleType' to be clear about RBAC
        isEmailVerified,
        isPhoneVerified,
        isTestUser
      } = req.body;

      if (!email || !password || !fullName || !username || !accountType || !accountCategory || !verificationMethod) {
        return res.status(400).json({
          success: false,
          message: 'Required fields: email, password, fullName, username, accountType, accountCategory, verificationMethod'
        });
      }

      // Validate role type if provided
      if (roleType && !Object.values(RoleType).includes(roleType)) {
        return res.status(400).json({
          success: false,
          message: `Invalid role type. Must be one of: ${Object.values(RoleType).join(', ')}`
        });
      }

      // Check if admin can assign this role level
      if (roleType) {
        const adminLevel = await RBACService.getUserHighestRoleLevel(user._id);
        const { Role } = await import('../models/Role');
        const roleToAssign = await Role.findOne({ name: roleType, isActive: true });
        
        if (roleToAssign && roleToAssign.level >= adminLevel && adminLevel < RoleLevel.SUPER_ADMIN) {
          return res.status(403).json({
            success: false,
            message: 'Cannot assign role with equal or higher privilege level than your own'
          });
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid email format'
        });
      }

      // Validate password strength
      if (password.length < 6) {
        return res.status(400).json({
          success: false,
          message: 'Password must be at least 6 characters long'
        });
      }

      // Validate account type and category
      if (!['MYSELF', 'SOMEONE_ELSE'].includes(accountType)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid account type. Must be MYSELF or SOMEONE_ELSE'
        });
      }

      if (!['PRIMARY_ACCOUNT', 'SECONDARY_ACCOUNT'].includes(accountCategory)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid account category. Must be PRIMARY_ACCOUNT or SECONDARY_ACCOUNT'
        });
      }

      if (!['PHONE', 'EMAIL'].includes(verificationMethod)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid verification method. Must be PHONE or EMAIL'
        });
      }

      const userData = {
        email,
        password,
        fullName,
        username,
        firstName,
        lastName,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
        countryOfResidence,
        phoneNumber,
        accountType,
        accountCategory,
        verificationMethod,
        roleType, // Pass RBAC role type instead of legacy role
        isEmailVerified,
        isPhoneVerified,
        isTestUser
      };

      const newUser = await adminUserService.createUser(userData);

      return res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: newUser
      });
    } catch (error) {
      logger.error('Error creating user:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create user'
      });
    }
  }

  /**
   * Get all users with pagination and filtering
   * @route GET /api/admin/users
   */
  static async getAllUsers(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['user', 'admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      // Parse pagination parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // Max 100 per page

      // Parse filters
      const filters: UserFilters = {};
      
      if (req.query.search) filters.search = req.query.search as string;
      if (req.query.role) filters.role = req.query.role as string;
      if (req.query.accountType) filters.accountType = req.query.accountType as string;
      if (req.query.isEmailVerified !== undefined) {
        filters.isEmailVerified = req.query.isEmailVerified === 'true';
      }
      if (req.query.isPhoneVerified !== undefined) {
        filters.isPhoneVerified = req.query.isPhoneVerified === 'true';
      }
      if (req.query.isTestUser !== undefined) {
        filters.isTestUser = req.query.isTestUser === 'true';
      }
      if (req.query.status) filters.status = req.query.status as 'active' | 'inactive' | 'banned';
      if (req.query.countryOfResidence) filters.countryOfResidence = req.query.countryOfResidence as string;
      
      // Parse date filters
      if (req.query.dateJoinedFrom) {
        filters.dateJoinedFrom = new Date(req.query.dateJoinedFrom as string);
      }
      if (req.query.dateJoinedTo) {
        filters.dateJoinedTo = new Date(req.query.dateJoinedTo as string);
      }
      if (req.query.lastLoginFrom) {
        filters.lastLoginFrom = new Date(req.query.lastLoginFrom as string);
      }
      if (req.query.lastLoginTo) {
        filters.lastLoginTo = new Date(req.query.lastLoginTo as string);
      }

      const result = await adminUserService.getAllUsers(page, limit, filters);

      return res.status(200).json({
        success: true,
        message: 'Users fetched successfully',
        data: result
      });
    } catch (error) {
      logger.error('Error fetching users:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch users'
      });
    }
  }

  /**
   * Get all admin users specifically (admin and superadmin roles only)
   */
  static async getAdminUsers(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      // Parse pagination
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

      // Parse filters
      const filters: UserFilters = {};
      
      if (req.query.search) filters.search = req.query.search as string;
      if (req.query.accountType) filters.accountType = req.query.accountType as string;
      if (req.query.isEmailVerified !== undefined) {
        filters.isEmailVerified = req.query.isEmailVerified === 'true';
      }
      if (req.query.isPhoneVerified !== undefined) {
        filters.isPhoneVerified = req.query.isPhoneVerified === 'true';
      }
      if (req.query.isTestUser !== undefined) {
        filters.isTestUser = req.query.isTestUser === 'true';
      }
      if (req.query.status) filters.status = req.query.status as 'active' | 'inactive' | 'banned';
      if (req.query.countryOfResidence) filters.countryOfResidence = req.query.countryOfResidence as string;
      
      // Parse date filters
      if (req.query.dateJoinedFrom) {
        filters.dateJoinedFrom = new Date(req.query.dateJoinedFrom as string);
      }
      if (req.query.dateJoinedTo) {
        filters.dateJoinedTo = new Date(req.query.dateJoinedTo as string);
      }
      if (req.query.lastLoginFrom) {
        filters.lastLoginFrom = new Date(req.query.lastLoginFrom as string);
      }
      if (req.query.lastLoginTo) {
        filters.lastLoginTo = new Date(req.query.lastLoginTo as string);
      }

      const result = await adminUserService.getAdminUsers(page, limit, filters);
      
      return res.status(200).json({
        success: true,
        message: 'Admin users retrieved successfully',
        data: result
      });
    } catch (error) {
      logger.error('Error fetching admin users:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch admin users'
      });
    }
  }

  /**
   * Get user by ID
   * @route GET /api/admin/users/:userId
   */
  static async getUserById(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;
      const foundUser = await adminUserService.getUserById(userId);

      if (!foundUser) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'User fetched successfully',
        data: foundUser
      });
    } catch (error) {
      logger.error('Error fetching user:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch user'
      });
    }
  }

  /**
   * Update user
   * @route PUT /api/admin/users/:userId
   */
  static async updateUser(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;
      const updatedUser = await adminUserService.updateUser(userId, req.body);

      return res.status(200).json({
        success: true,
        message: 'User updated successfully',
        data: updatedUser
      });
    } catch (error) {
      logger.error('Error updating user:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update user'
      });
    }
  }

  /**
   * Delete user
   * @route DELETE /api/admin/users/:userId
   */
  static async deleteUser(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;
      
      // Prevent admins from deleting themselves
      if (userId === user._id.toString()) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete your own account'
        });
      }

      // Only superadmin can delete other admins
      if (user.role === RoleType.ADMIN_USER) {
        const targetUser = await adminUserService.getUserById(userId);
        if (targetUser && [RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(targetUser.role as RoleType)) {
          return res.status(403).json({
            success: false,
            message: 'Only superadmin can delete admin accounts'
          });
        }
      }

      await adminUserService.deleteUser(userId);

      return res.status(200).json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting user:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete user'
      });
    }
  }

  /**
   * Ban/unban user
   * @route POST /api/admin/users/:userId/ban
   */
  static async toggleUserBan(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;
      const { banned, reason } = req.body;

      if (typeof banned !== 'boolean') {
        return res.status(400).json({
          success: false,
          message: 'banned field must be a boolean'
        });
      }

      // Prevent admins from banning themselves
      if (userId === user._id.toString()) {
        return res.status(400).json({
          success: false,
          message: 'Cannot ban your own account'
        });
      }

      // Only superadmin can ban other admins
      if (user.role === RoleType.ADMIN_USER) {
        const targetUser = await adminUserService.getUserById(userId);
        if (targetUser && [RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(targetUser.role as RoleType)) {
          return res.status(403).json({
            success: false,
            message: 'Only superadmin can ban admin accounts'
          });
        }
      }

      const result = await adminUserService.toggleUserBan(userId, banned, reason);

      // Handle different response types
      if (banned) {
        // For banning, result is a success message object
        return res.status(200).json({
          success: true,
          message: (result as { message: string; success: boolean }).message,
          data: null // User was deleted, no user data to return
        });
      } else {
        // For unbanning, result is the updated user object
        return res.status(200).json({
          success: true,
          message: 'User unbanned successfully',
          data: result
        });
      }
    } catch (error) {
      logger.error('Error toggling user ban:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update user ban status'
      });
    }
  }

  /**
   * Lock/unlock user account
   * @route POST /api/admin/users/:userId/lock
   */
  static async toggleUserLock(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;
      const { locked, reason } = req.body;

      if (typeof locked !== 'boolean') {
        return res.status(400).json({
          success: false,
          message: 'locked field must be a boolean'
        });
      }

      // Prevent admins from locking themselves
      if (userId === user._id.toString()) {
        return res.status(400).json({
          success: false,
          message: 'Cannot lock your own account'
        });
      }

      // Only superadmin can lock other admins
      if (user.role === 'admin') {
        const targetUser = await adminUserService.getUserById(userId);
        if (targetUser && ['admin', 'superadmin'].includes(targetUser.role)) {
          return res.status(403).json({
            success: false,
            message: 'Only superadmin can lock admin accounts'
          });
        }
      }

      const updatedUser = await adminUserService.toggleUserLock(userId, locked, reason);

      return res.status(200).json({
        success: true,
        message: `User account ${locked ? 'locked' : 'unlocked'} successfully`,
        data: updatedUser
      });
    } catch (error) {
      logger.error('Error toggling user lock:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update user lock status'
      });
    }
  }

  /**
   * Change user role
   * @route POST /api/admin/users/:userId/role
   */
  static async changeUserRole(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;
      const { role } = req.body;

      if (!role) {
        return res.status(400).json({
          success: false,
          message: 'Role is required'
        });
      }

      // Prevent changing own role
      if (userId === user._id.toString()) {
        return res.status(400).json({
          success: false,
          message: 'Cannot change your own role'
        });
      }

      // Only superadmin can promote users to admin or superadmin
      if (user.role === RoleType.ADMIN_USER && [RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(role as RoleType)) {
        return res.status(403).json({
          success: false,
          message: 'Only superadmin can promote users to admin roles'
        });
      }

      // Only superadmin can change admin roles
      if (user.role === RoleType.ADMIN_USER) {
        const targetUser = await adminUserService.getUserById(userId);
        if (targetUser && [RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(targetUser.role as RoleType)) {
          return res.status(403).json({
            success: false,
            message: 'Only superadmin can change admin roles'
          });
        }
      }

      const updatedUser = await adminUserService.changeUserRole(userId, role);

      return res.status(200).json({
        success: true,
        message: 'User role updated successfully',
        data: updatedUser
      });
    } catch (error) {
      logger.error('Error changing user role:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to change user role'
      });
    }
  }

  /**
   * Force 
   * verify user email or phone
   * @route POST /api/admin/users/:userId/verify
   */
  static async forceVerifyUser(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;
      const { type } = req.body;

      if (!type || !['email', 'phone'].includes(type)) {
        return res.status(400).json({
          success: false,
          message: 'Type must be either "email" or "phone"'
        });
      }

      const updatedUser = await adminUserService.forceVerifyUser(userId, type);

      return res.status(200).json({
        success: true,
        message: `User ${type} verified successfully`,
        data: updatedUser
      });
    } catch (error) {
      logger.error('Error force verifying user:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to verify user'
      });
    }
  }

  /**
   * Get user statistics
   * @route GET /api/admin/users/stats
   */
  static async getUserStats(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const stats = await adminUserService.getUserStats();

      return res.status(200).json({
        success: true,
        message: 'User statistics fetched successfully',
        data: stats
      });
    } catch (error) {
      logger.error('Error fetching user stats:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch user statistics'
      });
    }
  }

  /**
   * Bulk update multiple users
   * @route POST /api/admin/users/bulk-update
   */
  static async bulkUpdateUsers(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userIds, updateData } = req.body;

      if (!Array.isArray(userIds) || userIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'userIds must be a non-empty array'
        });
      }

      if (!updateData || typeof updateData !== 'object') {
        return res.status(400).json({
          success: false,
          message: 'updateData is required'
        });
      }

      const result = await adminUserService.bulkUpdateUsers(userIds, updateData);

      return res.status(200).json({
        success: true,
        message: 'Bulk update completed',
        data: result
      });
    } catch (error) {
      logger.error('Error in bulk update:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update users'
      });
    }
  }

  /**
   * Bulk delete multiple users
   * @route POST /api/admin/users/bulk-delete
   */
  static async bulkDeleteUsers(req: Request, res: Response) {
    try {
      const user = req.user! as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { userIds, reason } = req.body;

      if (!Array.isArray(userIds) || userIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'userIds must be a non-empty array'
        });
      }

      if (userIds.length > 100) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete more than 100 users at once'
        });
      }

      // Validate that all IDs are strings
      const invalidIds = userIds.filter(id => typeof id !== 'string' || !id.trim());
      if (invalidIds.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'All user IDs must be valid strings'
        });
      }

      const result = await adminUserService.bulkDeleteUsers(
        userIds, 
        user._id.toString(), 
        reason
      );

      // Create detailed summary message
      const summaryMessage = `Successfully deleted ${result.data.successful} of ${result.data.attempted} users. ` +
        `Cleanup: ${result.data.profilesDeleted} profiles, ${result.data.vaultsDeleted} vaults, ` +
        `${result.data.analyticsDeleted} analytics records, ${result.data.securityRecordsDeleted} security events, ` +
        `${result.data.deviceFingerprintsDeleted} device fingerprints, ${result.data.listsDeleted} lists, ` +
        `${result.data.tasksDeleted} tasks, ${result.data.interactionsDeleted} interactions, ` +
        `${result.data.referralConnectionsUpdated} referral connections updated.`;

      logger.info(summaryMessage, {
        adminUserId: user._id,
        userIds,
        reason,
        result: result.data
      });

      res.status(200).json({
        success: true,
        message: summaryMessage,
        data: result.data,
        cleanupDetails: {
          userDeletion: {
            attempted: result.data.attempted,
            successful: result.data.successful,
            failed: result.data.failed,
            errors: result.data.errors
          },
          profileCleanup: {
            profilesDeleted: result.data.profilesDeleted,
            vaultsDeleted: result.data.vaultsDeleted,
            vaultItemsDeleted: result.data.vaultItemsDeleted,
            vaultActivitiesDeleted: result.data.vaultActivitiesDeleted
          },
          contentCleanup: {
            listsDeleted: result.data.listsDeleted,
            tasksDeleted: result.data.tasksDeleted,
            interactionsDeleted: result.data.interactionsDeleted
          },
          securityCleanup: {
            securityRecordsDeleted: result.data.securityRecordsDeleted,
            deviceFingerprintsDeleted: result.data.deviceFingerprintsDeleted,
            fraudAttemptsDeleted: result.data.fraudAttemptsDeleted,
            ipTrackingDeleted: result.data.ipTrackingDeleted,
            verificationRecordsDeleted: result.data.verificationRecordsDeleted
          },
          socialCleanup: {
            groupInvitationsDeleted: result.data.groupInvitationsDeleted,
            referralConnectionsUpdated: result.data.referralConnectionsUpdated
          },
          analyticsCleanup: {
            analyticsDeleted: result.data.analyticsDeleted
          }
        }
      });
    } catch (error) {
      logger.error('Error in comprehensive bulk delete:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete users'
      });
    }
  }

  /**
   * Search users
   * @route GET /api/admin/users/search
   */
  static async searchUsers(req: Request, res: Response) {
    try {
      const user = req.user as any;
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      const { q: searchTerm, limit } = req.query;

      if (!searchTerm) {
        return res.status(400).json({
          success: false,
          message: 'Search term (q) is required'
        });
      }

      const limitNum = Math.min(parseInt(limit as string) || 10, 50); // Max 50 results
      const users = await adminUserService.searchUsers(searchTerm as string, limitNum);

      return res.status(200).json({
        success: true,
        message: 'Search completed successfully',
        data: users
      });
    } catch (error) {
      logger.error('Error searching users:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to search users'
      });
    }
  }

  /**
   * Get user contacts information
   * @route GET /api/admin/users/contacts
   */
  static async getUserContacts(req: Request, res: Response) {
    try {
      const user = req.user as any;
      // if (!user || !['admin', 'superadmin'].includes(user.role)) {
      //   return res.status(403).json({
      //     success: false,
      //     message: 'Admin access required'
      //   });
      // }

      // Parse pagination parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // Max 100 per page

      // Parse filters
      const filters: UserFilters = {};
      
      if (req.query.search) filters.search = req.query.search as string;
      if (req.query.role) filters.role = req.query.role as string;
      if (req.query.accountType) filters.accountType = req.query.accountType as string;
      if (req.query.isEmailVerified !== undefined) {
        filters.isEmailVerified = req.query.isEmailVerified === 'true';
      }
      if (req.query.isPhoneVerified !== undefined) {
        filters.isPhoneVerified = req.query.isPhoneVerified === 'true';
      }
      if (req.query.isTestUser !== undefined) {
        filters.isTestUser = req.query.isTestUser === 'true';
      }
      if (req.query.status) filters.status = req.query.status as 'active' | 'inactive' | 'banned';
      if (req.query.countryOfResidence) filters.countryOfResidence = req.query.countryOfResidence as string;
      
      // Parse date filters
      if (req.query.dateJoinedFrom) {
        filters.dateJoinedFrom = new Date(req.query.dateJoinedFrom as string);
      }
      if (req.query.dateJoinedTo) {
        filters.dateJoinedTo = new Date(req.query.dateJoinedTo as string);
      }

      const result = await adminUserService.getUserContacts(page, limit, filters);

      return res.status(200).json({
        success: true,
        message: 'User contacts fetched successfully',
        data: result
      });
    } catch (error) {
      logger.error('Error fetching user contacts:', error);
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch user contacts'
      });
    }
  }

  /**
   * Delete all test users
   */
  static async deleteTestUsers(req: Request, res: Response) {
    try {
      const user = req.user as any;
      
      if (!user || !['admin', 'superadmin'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      // Find all test users
      const testUsers = await User.find({ isTestUser: true })
        .select('_id email fullName username secondaryId role')
        .lean();

      if (testUsers.length === 0) {
        return res.status(200).json({
          success: true,
          message: 'No test users found to delete',
          data: {
            attempted: 0,
            successful: 0,
            failed: 0,
            errors: []
          }
        });
      }

      // Extract user IDs
      const testUserIds = testUsers.map((user: any) => user._id.toString());

      // Use the existing bulk delete method to delete test users
      const result = await adminUserService.bulkDeleteUsers(
        testUserIds,
        user._id.toString(),
        'Bulk deletion of test users'
      );

      logger.info(`Test users deletion completed by admin ${user._id}`, {
        totalTestUsers: testUsers.length,
        result
      });

      return res.status(200).json({
        success: result.success,
        message: `Test users deletion completed. ${result.data.successful} users deleted successfully.`,
        data: {
          ...result.data,
          testUsersFound: testUsers.length,
          testUsersDeleted: result.data.successful
        }
      });

    } catch (error) {
      logger.error('Error deleting test users:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to delete test users',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
} 