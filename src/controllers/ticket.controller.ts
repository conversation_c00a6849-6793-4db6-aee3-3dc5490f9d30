import { Request, Response } from 'express';
import asyncHandler from 'express-async-handler';
import createHttpError from 'http-errors';
import { ticketService, CreateTicketData, UpdateTicketData, TicketFilters } from '../services/ticket.service';
import { TicketStatus, TicketPriority, TicketCategory } from '../models/ticket.model';
import { RoleType } from '../models/Role';
import { logger } from '../utils/logger';

// @desc    Create a new ticket
// @route   POST /api/tickets
// @access  Private
export const createTicket = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { title, description, category, priority, tags, dueDate, mptsReward, isPublic, allowComments, notifyOnUpdate } = req.body;

  if (!title || !description) {
    throw createHttpError(400, 'Title and description are required');
  }

  const ticketData: CreateTicketData = {
    title,
    description,
    category: category || TicketCategory.GENERAL,
    priority: priority || TicketPriority.MEDIUM,
    tags: tags || [],
    dueDate: dueDate ? new Date(dueDate) : undefined,
    mptsReward: mptsReward || 5,
    isPublic: isPublic !== undefined ? isPublic : true,
    allowComments: allowComments !== undefined ? allowComments : true,
    notifyOnUpdate: notifyOnUpdate !== undefined ? notifyOnUpdate : true
  };

  const ticket = await ticketService.createTicket(ticketData, user._id);

  res.status(201).json({
    success: true,
    data: ticket,
    message: 'Ticket created successfully'
  });
});

// @desc    Get all tickets with filters
// @route   GET /api/tickets
// @access  Private
export const getTickets = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const {
    status,
    priority,
    category,
    assignedTo,
    createdBy,
    tags,
    search,
    dateFrom,
    dateTo,
    isPublic,
    page = 1,
    limit = 20
  } = req.query;

  const filters: TicketFilters = {};

  if (status) {
    filters.status = Array.isArray(status) ? status as TicketStatus[] : [status as TicketStatus];
  }

  if (priority) {
    filters.priority = Array.isArray(priority) ? priority as TicketPriority[] : [priority as TicketPriority];
  }

  if (category) {
    filters.category = Array.isArray(category) ? category as TicketCategory[] : [category as TicketCategory];
  }

  if (assignedTo) {
    filters.assignedTo = assignedTo as string;
  }

  if (createdBy) {
    filters.createdBy = createdBy as string;
  }

  if (tags) {
    filters.tags = Array.isArray(tags) ? tags as string[] : [tags as string];
  }

  if (search) {
    filters.search = search as string;
  }

  if (dateFrom) {
    filters.dateFrom = new Date(dateFrom as string);
  }

  if (dateTo) {
    filters.dateTo = new Date(dateTo as string);
  }

  if (isPublic !== undefined) {
    filters.isPublic = isPublic === 'true';
  }

  const result = await ticketService.getTickets(filters, Number(page), Number(limit));

  res.status(200).json({
    success: true,
    data: result.tickets,
    pagination: {
      page: result.page,
      totalPages: result.totalPages,
      total: result.total,
      limit: Number(limit)
    }
  });
});

// @desc    Get ticket by ID
// @route   GET /api/tickets/:id
// @access  Private
export const getTicketById = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { id } = req.params;

  if (!id) {
    throw createHttpError(400, 'Ticket ID is required');
  }

  const ticket = await ticketService.getTicketById(id);

  if (!ticket) {
    throw createHttpError(404, 'Ticket not found');
  }

  // Check if user has access to this ticket
  const isAdmin = user.role === 'admin' || user.role === 'superadmin';
  const isCreator = ticket.createdBy._id.toString() === user._id.toString();
  const isAssigned = ticket.assignedTo && ticket.assignedTo._id.toString() === user._id.toString();
  const isPublic = ticket.isPublic;

  if (!isAdmin && !isCreator && !isAssigned && !isPublic) {
    throw createHttpError(403, 'Access denied to this ticket');
  }

  res.status(200).json({
    success: true,
    data: ticket
  });
});

// @desc    Update ticket
// @route   PUT /api/tickets/:id
// @access  Private
export const updateTicket = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { id } = req.params;
  const updateData: UpdateTicketData = req.body;

  if (!id) {
    throw createHttpError(400, 'Ticket ID is required');
  }

  const ticket = await ticketService.getTicketById(id);

  if (!ticket) {
    throw createHttpError(404, 'Ticket not found');
  }

  // Check permissions
  const isAdmin = user.role === 'admin' || user.role === 'superadmin';
  const isCreator = ticket.createdBy._id.toString() === user._id.toString();
  const isAssigned = ticket.assignedTo && ticket.assignedTo._id.toString() === user._id.toString();

  if (!isAdmin && !isCreator && !isAssigned) {
    throw createHttpError(403, 'You can only update tickets you created or are assigned to');
  }

  // Only admins can update certain fields
  if (!isAdmin) {
    delete updateData.mptsReward;
    delete updateData.isPublic;
  }

  const updatedTicket = await ticketService.updateTicket(id, updateData, user._id);

  res.status(200).json({
    success: true,
    data: updatedTicket,
    message: 'Ticket updated successfully'
  });
});

// @desc    Assign ticket to user
// @route   POST /api/tickets/:id/assign
// @access  Private (Admin only)
export const assignTicket = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  // Check if user is admin
  if (user.role !== 'admin' && user.role !== 'superadmin') {
    throw createHttpError(403, 'Admin access required');
  }

  const { id } = req.params;
  const { userId } = req.body;

  if (!id) {
    throw createHttpError(400, 'Ticket ID is required');
  }

  if (!userId) {
    throw createHttpError(400, 'User ID is required');
  }

  const assignedTicket = await ticketService.assignTicket(id, userId, user._id);

  res.status(200).json({
    success: true,
    data: assignedTicket,
    message: 'Ticket assigned successfully'
  });
});

// @desc    Update ticket status
// @route   PUT /api/tickets/:id/status
// @access  Private
export const updateTicketStatus = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { id } = req.params;
  const { status } = req.body;

  if (!id) {
    throw createHttpError(400, 'Ticket ID is required');
  }

  if (!status || !Object.values(TicketStatus).includes(status)) {
    throw createHttpError(400, 'Valid status is required');
  }

  const ticket = await ticketService.getTicketById(id);

  if (!ticket) {
    throw createHttpError(404, 'Ticket not found');
  }

  // Check permissions
  const isAdmin = user.role === 'admin' || user.role === 'superadmin';
  const isCreator = ticket.createdBy._id.toString() === user._id.toString();
  const isAssigned = ticket.assignedTo && ticket.assignedTo._id.toString() === user._id.toString();

  if (!isAdmin && !isCreator && !isAssigned) {
    throw createHttpError(403, 'You can only update status of tickets you created or are assigned to');
  }

  const updatedTicket = await ticketService.updateTicketStatus(id, status, user._id);

  res.status(200).json({
    success: true,
    data: updatedTicket,
    message: 'Ticket status updated successfully'
  });
});

// @desc    Award MPTs for completed ticket
// @route   POST /api/tickets/:id/award-mpts
// @access  Private
export const awardMPTsForTicket = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { id } = req.params;

  if (!id) {
    throw createHttpError(400, 'Ticket ID is required');
  }

  const ticket = await ticketService.getTicketById(id);

  if (!ticket) {
    throw createHttpError(404, 'Ticket not found');
  }

  // Check if user is assigned to this ticket
  const isAssigned = ticket.assignedTo && ticket.assignedTo._id.toString() === user._id.toString();
  const isAdmin = user.role === 'admin' || user.role === 'superadmin';

  if (!isAssigned && !isAdmin) {
    throw createHttpError(403, 'You can only award MPTs for tickets assigned to you');
  }

  if (ticket.status !== TicketStatus.COMPLETED) {
    throw createHttpError(400, 'Can only award MPTs for completed tickets');
  }

  if (ticket.mptsAwarded) {
    throw createHttpError(400, 'MPTs already awarded for this ticket');
  }

  const result = await ticketService.awardMPTsForTicket(id, user._id);

  res.status(200).json({
    success: true,
    data: {
      ticket: result.ticket,
      mptsTransaction: result.mptsTransaction
    },
    message: `${result.ticket.mptsReward} MPTs awarded successfully`
  });
});

// @desc    Add comment to ticket
// @route   POST /api/tickets/:id/comments
// @access  Private
export const addComment = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { id } = req.params;
  const { text, isInternal = false } = req.body;

  if (!id) {
    throw createHttpError(400, 'Ticket ID is required');
  }

  if (!text) {
    throw createHttpError(400, 'Comment text is required');
  }

  const ticket = await ticketService.getTicketById(id);

  if (!ticket) {
    throw createHttpError(404, 'Ticket not found');
  }

  // Check permissions
  const isAdmin = user.role === RoleType.ADMIN_USER || user.role === RoleType.SUPER_ADMIN;
  const isCreator = ticket.createdBy._id.toString() === user._id.toString();
  const isAssigned = ticket.assignedTo && ticket.assignedTo._id.toString() === user._id.toString();

  if (!isAdmin && !isCreator && !isAssigned) {
    throw createHttpError(403, 'You can only comment on tickets you created or are assigned to');
  }

  // Only admins can add internal comments
  if (isInternal && !isAdmin) {
    throw createHttpError(403, 'Only admins can add internal comments');
  }

  const updatedTicket = await ticketService.addComment(id, text, user._id, isInternal);

  res.status(200).json({
    success: true,
    data: updatedTicket,
    message: 'Comment added successfully'
  });
});

// @desc    Get ticket statistics
// @route   GET /api/tickets/stats
// @access  Private (Admin only)
export const getTicketStats = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  // Check if user is admin
  if (user.role !== 'admin' && user.role !== 'superadmin') {
    throw createHttpError(403, 'Admin access required');
  }

  const filters: TicketFilters = {};

  // Apply filters from query params
  if (req.query.assignedTo) {
    filters.assignedTo = req.query.assignedTo as string;
  }

  if (req.query.createdBy) {
    filters.createdBy = req.query.createdBy as string;
  }

  const stats = await ticketService.getTicketStats(filters);

  res.status(200).json({
    success: true,
    data: stats
  });
});

// @desc    Get tickets assigned to current user
// @route   GET /api/tickets/my-assigned
// @access  Private
export const getMyAssignedTickets = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { status } = req.query;
  const statusFilter = status ? (Array.isArray(status) ? status as TicketStatus[] : [status as TicketStatus]) : undefined;

  const tickets = await ticketService.getTicketsAssignedToUser(user._id.toString(), statusFilter);

  res.status(200).json({
    success: true,
    data: tickets
  });
});

// @desc    Get tickets created by current user
// @route   GET /api/tickets/my-created
// @access  Private
export const getMyCreatedTickets = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  const { status } = req.query;
  const statusFilter = status ? (Array.isArray(status) ? status as TicketStatus[] : [status as TicketStatus]) : undefined;

  const tickets = await ticketService.getTicketsCreatedByUser(user._id.toString(), statusFilter);

  res.status(200).json({
    success: true,
    data: tickets
  });
});

// @desc    Delete ticket
// @route   DELETE /api/tickets/:id
// @access  Private (Admin only)
export const deleteTicket = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as any;
  
  if (!user) {
    throw createHttpError(401, 'Not authenticated');
  }

  // Check if user is admin
  if (user.role !== 'admin' && user.role !== 'superadmin') {
    throw createHttpError(403, 'Admin access required');
  }

  const { id } = req.params;

  if (!id) {
    throw createHttpError(400, 'Ticket ID is required');
  }

  await ticketService.deleteTicket(id, user._id);

  res.status(200).json({
    success: true,
    message: 'Ticket deleted successfully'
  });
}); 