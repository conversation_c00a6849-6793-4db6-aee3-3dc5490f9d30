/**
 * @file activity-logs.controller.ts
 * @description Activity Logs API Controller with Role-Based Access Control
 * ======================================================================
 * 
 * This controller provides comprehensive endpoints for account-level and 
 * profile-level activity logging with advanced filtering, analytics, and 
 * role-based access control.
 */

import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { AccountActivityModel } from '../models/account-activity.model';
import { ProfileActivityModel } from '../models/gamification/user-activity.model';
import { 
  IActivityFilter, 
  IActivityLogResponse, 
  IActivityAnalytics,
  AccountActivityCategory,
  ProfileActivityCategory,
  ActivityVisibility 
} from '../interfaces/activity-log.interface';
import { logger } from '../utils/logger';
import { auditLogService } from '../services/auditLog.service';
import { activitySearchService } from '../services/activity-search.service';
import { activityAnalyticsService } from '../services/activity-analytics.service';
import { RoleType } from '../models/Role';

interface ActivityRequestUser {
  _id: string;
  id: string;
  role: RoleType
  email: string;
}

interface ActivityRequest {
  user?: ActivityRequestUser;
  profileId?: string;
  accountId?: string;
  params: {
    accountId?: string;
    profileId?: string;
    userId?: string;
    [key: string]: string | undefined;
  };
  query: {
    [key: string]: any;
  };
  body: any;
  ip: string;
  get(name: string): string | undefined;
  session?: {
    id?: string;
  };
}

export class ActivityLogsController {
  
  // ============================================================================
  // Account Activity Endpoints
  // ============================================================================

  /**
   * Get account activities with role-based filtering
   */
  async getAccountActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const { accountId } = req.params;
      const user = req.user;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Validate account ID format
      if (!accountId || !mongoose.Types.ObjectId.isValid(accountId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid account ID format'
        });
        return;
      }

      // Validate account access
      if (!this.canAccessAccount(user, accountId)) {
        res.status(403).json({
          success: false,
          message: 'Access denied to account activities'
        });
        return;
      }

      let filters;
      try {
        filters = this.buildFiltersFromQuery(req.query);
      } catch (error) {
        res.status(400).json({
          success: false,
          message: error instanceof Error ? error.message : 'Invalid request parameters'
        });
        return;
      }

      const accountObjectId = new mongoose.Types.ObjectId(accountId);
      const userObjectId = new mongoose.Types.ObjectId(user.id);

      // Build database query with role-based filtering
      const query = await this.buildDatabaseQuery(accountObjectId, filters, 'account');
      
      // Apply role-based filtering
      if (user.role === RoleType.REGULAR_USER) {
        query.userId = userObjectId;
      }
      
      // Execute query with pagination
      const page = filters.page || 1;
      const limit = Math.min(filters.limit || 50, 500);
      const skip = (page - 1) * limit;
      
      const activities = await AccountActivityModel.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      logger.debug('Account activities query executed', {
        resultCount: activities.length,
        accountId,
        userId: user.id,
        filtersApplied: filters
      });

      // Apply role-based data filtering
      const filteredActivities = activities.map((activity: any) => {
        const safeActivity: any = {
          _id: activity._id,
          activityType: activity.activityType,
          category: activity.category,
          timestamp: activity.timestamp,
          accountId: activity.accountId,
          userId: activity.userId,
          profileId: activity.profileId,
          userRole: user.role,
          isRewardable: activity.isRewardable,
          isBonusable: activity.isBonusable,
          isSystemGenerated: activity.isSystemGenerated,
          source: activity.source
        };

        // Include metadata based on user role
        if (user.role === RoleType.SUPER_ADMIN || user.role === RoleType.ADMIN_USER) {
          safeActivity.metadata = activity.metadata || {};
          safeActivity.ipAddress = activity.ipAddress;
          safeActivity.userAgent = activity.userAgent;
        } else {
          // For regular users, only include safe metadata
          safeActivity.metadata = activity.metadata ? {
            source: activity.metadata.source,
            category: activity.metadata.category
          } : {};
        }

        return safeActivity;
      });

      const response: IActivityLogResponse = {
        success: true,
        data: filteredActivities,
        pagination: this.buildPagination(req.query, filteredActivities.length),
        filters
      };

      // Log this access for audit purposes
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'VIEW_ACCOUNT_ACTIVITIES',
        details: {
          accountId,
          filtersApplied: filters,
          resultCount: filteredActivities.length
        }
      });

      res.json(response);
    } catch (error) {
      logger.error('Error fetching account activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch account activities'
      });
    }
  }

  /**
   * Get account activity analytics
   */
  async getAccountActivityAnalytics(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const { accountId } = req.params;
      const user = req.user;
      
      if (!user || !accountId || !this.canAccessAccount(user, accountId)) {
        res.status(403).json({
          success: false,
          message: 'Access denied to account analytics'
        });
        return;
      }

      const timeRange = this.buildTimeRange(req.query);
      const accountObjectId = new mongoose.Types.ObjectId(accountId);

      // Build query with time range filter
      let matchQuery: any = { accountId: accountObjectId };
      if (timeRange && timeRange.length === 2) {
        matchQuery.timestamp = {
          $gte: timeRange[0],
          $lte: timeRange[1]
        };
      }

      // Execute comprehensive analytics aggregation
      const analyticsResult = await AccountActivityModel.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: null,
            totalActivities: { $sum: 1 },
            rewardableActivities: { $sum: { $cond: ['$isRewardable', 1, 0] } },
            bonusableActivities: { $sum: { $cond: ['$isBonusable', 1, 0] } },
            systemActivities: { $sum: { $cond: ['$isSystemGenerated', 1, 0] } },
            uniqueProfiles: { $addToSet: '$profileId' },
            categoriesBreakdown: { $push: '$category' },
            activityTypesBreakdown: { $push: '$activityType' }
          }
        },
        {
          $project: {
            totalActivities: 1,
            rewardableActivities: 1,
            bonusableActivities: 1,
            systemActivities: 1,
            uniqueProfilesCount: { $size: '$uniqueProfiles' },
            categories: '$categoriesBreakdown',
            activityTypes: '$activityTypesBreakdown'
          }
        }
      ]);

      const analytics = analyticsResult[0] || {
        totalActivities: 0,
        rewardableActivities: 0,
        bonusableActivities: 0,
        systemActivities: 0,
        uniqueProfilesCount: 0,
        categories: [],
        activityTypes: []
      };

      // Apply role-based analytics filtering
      const filteredAnalytics = this.filterAnalyticsByRole(analytics, user.role);

      logger.debug('Account analytics generated', {
        accountId,
        analytics: filteredAnalytics,
        matchQuery,
        timeRange
      });

      res.json({
        success: true,
        data: filteredAnalytics,
        accountId,
        timeRange: timeRange ? {
          start: timeRange[0],
          end: timeRange[1]
        } : undefined
      });

      // Log analytics access
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'VIEW_ACCOUNT_ANALYTICS',
        details: { accountId }
      });

    } catch (error) {
      logger.error('Error fetching account analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch account analytics'
      });
    }
  }

  /**
   * Log new account activity
   */
  async logAccountActivity(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const { accountId } = req.params;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const activityData = {
        ...req.body,
        accountId: new mongoose.Types.ObjectId(accountId),
        userId: new mongoose.Types.ObjectId(user.id),
        userRole: user.role,
        timestamp: new Date(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        source: 'api'
      };

      // Validate activity data before creation
      if (!activityData.activityType || !activityData.category) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: activityType and category are required'
        });
        return;
      }

      try {
        const activity = await AccountActivityModel.create(activityData);

        logger.info('Account activity created successfully', {
          activityId: activity._id,
          activityType: activityData.activityType,
          accountId,
          userId: user.id
        });

        res.status(201).json({
          success: true,
          data: {
            _id: activity._id,
            activityType: activity.activityType,
            category: activity.category,
            timestamp: activity.timestamp,
            accountId: activity.accountId,
            userId: activity.userId,
            isRewardable: activity.isRewardable,
            isBonusable: activity.isBonusable
          },
          message: 'Account activity logged successfully'
        });
      } catch (dbError) {
        logger.error('Database error creating account activity:', dbError);
        res.status(500).json({
          success: false,
          message: 'Database error while creating activity'
        });
        return;
      }

    } catch (error) {
      logger.error('Error logging account activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to log account activity'
      });
    }
  }

  // ============================================================================
  // Profile Activity Endpoints
  // ============================================================================

  /**
   * Get profile activities with role-based filtering
   */
  async getProfileActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const { profileId } = req.params;
      const user = req.user;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Validate profile ID format
      if (!profileId || !mongoose.Types.ObjectId.isValid(profileId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid profile ID format'
        });
        return;
      }

      // Validate profile access
      if (!(await this.canAccessProfile(user, profileId))) {
        res.status(403).json({
          success: false,
          message: 'Access denied to profile activities'
        });
        return;
      }

      let filters;
      try {
        filters = this.buildFiltersFromQuery(req.query);
      } catch (error) {
        res.status(400).json({
          success: false,
          message: error instanceof Error ? error.message : 'Invalid request parameters'
        });
        return;
      }

      const profileObjectId = new mongoose.Types.ObjectId(profileId);
      const userObjectId = new mongoose.Types.ObjectId(user.id);

      // Build database query with role-based filtering
      const query = await this.buildDatabaseQuery(profileObjectId, filters, 'profile');
      
      // Apply role-based filtering
      if (user.role === RoleType.REGULAR_USER) {
        query.userId = userObjectId;
        // Only show activities the user is allowed to see
        query.$or = [
          { visibility: 'public' },
          { visibility: 'connections_only' },
          { visibility: 'private', userId: userObjectId }
        ];
      }
      
      // Execute query with pagination
      const page = filters.page || 1;
      const limit = Math.min(filters.limit || 50, 500);
      const skip = (page - 1) * limit;
      
      const activities = await ProfileActivityModel.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      logger.debug('Profile activities query executed', {
        resultCount: activities.length,
        profileId,
        userId: user.id,
        filtersApplied: filters
      });

      // Apply role-based data filtering
      const filteredActivities = activities.map((activity: any) => {
        const safeActivity: any = {
          _id: activity._id,
          activityType: activity.activityType,
          category: activity.category,
          timestamp: activity.timestamp,
          profileId: activity.profileId,
          accountId: activity.accountId,
          userId: activity.userId,
          userRole: user.role,
          isRewardable: activity.isRewardable,
          isBonusable: activity.isBonusable,
          isSystemGenerated: activity.isSystemGenerated,
          visibility: activity.visibility,
          myPtsEarned: activity.myPtsEarned || 0,
          myPtsSpent: activity.myPtsSpent || 0,
          source: activity.source
        };

        // Include metadata based on user role
        if (user.role === RoleType.SUPER_ADMIN || user.role === RoleType.ADMIN_USER) {
          safeActivity.metadata = activity.metadata || {};
          safeActivity.ipAddress = activity.ipAddress;
          safeActivity.userAgent = activity.userAgent;
          safeActivity.badgeEarned = activity.badgeEarned;
          safeActivity.milestoneReached = activity.milestoneReached;
        } else {
          // For regular users, only include safe metadata
          safeActivity.metadata = activity.metadata ? {
            source: activity.metadata.source,
            category: activity.metadata.category
          } : {};
        }

        return safeActivity;
      });

      const response: IActivityLogResponse = {
        success: true,
        data: filteredActivities,
        pagination: this.buildPagination(req.query, filteredActivities.length),
        filters
      };

      // Log this access for audit purposes
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'VIEW_PROFILE_ACTIVITIES',
        details: {
          profileId,
          filtersApplied: filters,
          resultCount: filteredActivities.length
        }
      });

      res.json(response);
    } catch (error) {
      logger.error('Error fetching profile activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch profile activities'
      });
    }
  }

  /**
   * Get profile activity analytics
   */
  async getProfileActivityAnalytics(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const { profileId } = req.params;
      const user = req.user;
      
      if (!user || !profileId || !(await this.canAccessProfile(user, profileId))) {
        res.status(403).json({
          success: false,
          message: 'Access denied to profile analytics'
        });
        return;
      }

      const timeRange = this.buildTimeRange(req.query);
      const profileObjectId = new mongoose.Types.ObjectId(profileId);

      // Build query with time range filter
      let matchQuery: any = { profileId: profileObjectId };
      if (timeRange && timeRange.length === 2) {
        matchQuery.timestamp = {
          $gte: timeRange[0],
          $lte: timeRange[1]
        };
      }

      // Execute comprehensive analytics aggregation
      const analyticsResult = await ProfileActivityModel.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: null,
            totalActivities: { $sum: 1 },
            rewardableActivities: { $sum: { $cond: ['$isRewardable', 1, 0] } },
            bonusableActivities: { $sum: { $cond: ['$isBonusable', 1, 0] } },
            systemActivities: { $sum: { $cond: ['$isSystemGenerated', 1, 0] } },
            totalMyPtsEarned: { $sum: '$myPtsEarned' },
            totalMyPtsSpent: { $sum: '$myPtsSpent' },
            uniqueBadges: { $addToSet: '$badgeEarned' },
            uniqueMilestones: { $addToSet: '$milestoneReached' },
            categoriesBreakdown: { $push: '$category' },
            activityTypesBreakdown: { $push: '$activityType' }
          }
        },
        {
          $project: {
            totalActivities: 1,
            rewardableActivities: 1,
            bonusableActivities: 1,
            systemActivities: 1,
            totalMyPtsEarned: 1,
            totalMyPtsSpent: 1,
            myPtsNet: { $subtract: ['$totalMyPtsEarned', '$totalMyPtsSpent'] },
            uniqueBadgeCount: { 
              $size: { 
                $filter: { 
                  input: '$uniqueBadges', 
                  cond: { $ne: ['$$this', null] } 
                } 
              } 
            },
            uniqueMilestoneCount: { 
              $size: { 
                $filter: { 
                  input: '$uniqueMilestones', 
                  cond: { $ne: ['$$this', null] } 
                } 
              } 
            },
            categories: '$categoriesBreakdown',
            activityTypes: '$activityTypesBreakdown'
          }
        }
      ]);

      const analytics = analyticsResult[0] || {
        totalActivities: 0,
        rewardableActivities: 0,
        bonusableActivities: 0,
        systemActivities: 0,
        totalMyPtsEarned: 0,
        totalMyPtsSpent: 0,
        myPtsNet: 0,
        uniqueBadgeCount: 0,
        uniqueMilestoneCount: 0,
        categories: [],
        activityTypes: []
      };

      logger.debug('Profile analytics generated', {
        profileId,
        analytics,
        matchQuery,
        timeRange
      });

      res.json({
        success: true,
        data: analytics,
        profileId,
        timeRange: timeRange ? {
          start: timeRange[0],
          end: timeRange[1]
        } : undefined
      });

      // Log analytics access
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'VIEW_PROFILE_ANALYTICS',
        details: { profileId }
      });

    } catch (error) {
      logger.error('Error fetching profile analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch profile analytics'
      });
    }
  }

  /**
   * Get MyPts activities for a profile
   */
  async getMyPtsActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const { profileId } = req.params;
      const user = req.user;
      
      if (!user || !profileId || !(await this.canAccessProfile(user, profileId))) {
        res.status(403).json({
          success: false,
          message: 'Access denied to MyPts activities'
        });
        return;
      }

      let filters;
      try {
        filters = this.buildFiltersFromQuery(req.query);
      } catch (error) {
        res.status(400).json({
          success: false,
          message: error instanceof Error ? error.message : 'Invalid request parameters'
        });
        return;
      }

      const profileObjectId = new mongoose.Types.ObjectId(profileId);

      // Use model's specialized MyPts method
      const activities = await ProfileActivityModel.getMyPtsActivities(
        profileObjectId,
        filters
      );

      logger.debug('MyPts activities query executed', {
        resultCount: activities.length,
        profileId,
        userId: user.id,
        filtersApplied: filters
      });

      const filteredActivities = activities.map((activity: any) => ({
        _id: activity._id,
        activityType: activity.activityType,
        category: activity.category,
        timestamp: activity.timestamp,
        profileId: activity.profileId,
        accountId: activity.accountId,
        myPtsEarned: activity.myPtsEarned || 0,
        myPtsSpent: activity.myPtsSpent || 0,
        badgeEarned: activity.badgeEarned,
        milestoneReached: activity.milestoneReached,
        visibility: activity.visibility,
        isRewardable: activity.isRewardable,
        metadata: activity.metadata || {}
      }));

      res.json({
        success: true,
        data: filteredActivities,
        pagination: this.buildPagination(req.query, filteredActivities.length)
      });

    } catch (error) {
      logger.error('Error fetching MyPts activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch MyPts activities'
      });
    }
  }

  /**
   * Log new profile activity
   */
  async logProfileActivity(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const { profileId } = req.params;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const activityData = {
        ...req.body,
        profileId: new mongoose.Types.ObjectId(profileId),
        accountId: new mongoose.Types.ObjectId(req.body.accountId || user.id),
        userId: new mongoose.Types.ObjectId(user.id),
        userRole: user.role,
        timestamp: new Date(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        source: 'api'
      };

      // Validate activity data before creation
      if (!activityData.activityType || !activityData.category) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: activityType and category are required'
        });
        return;
      }

      try {
        const activity = await ProfileActivityModel.create(activityData);

        logger.info('Profile activity created successfully', {
          activityId: activity._id,
          activityType: activityData.activityType,
          profileId,
          userId: user.id
        });

        res.status(201).json({
          success: true,
          data: {
            _id: activity._id,
            activityType: activity.activityType,
            category: activity.category,
            timestamp: activity.timestamp,
            profileId: activity.profileId,
            accountId: activity.accountId,
            userId: activity.userId,
            isRewardable: activity.isRewardable,
            isBonusable: activity.isBonusable,
            myPtsEarned: activity.myPtsEarned || 0,
            myPtsSpent: activity.myPtsSpent || 0,
            visibility: activity.visibility
          },
          message: 'Profile activity logged successfully'
        });
      } catch (dbError) {
        logger.error('Database error creating profile activity:', dbError);
        res.status(500).json({
          success: false,
          message: 'Database error while creating activity'
        });
        return;
      }

    } catch (error) {
      logger.error('Error logging profile activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to log profile activity'
      });
    }
  }

  // ============================================================================
  // Cross-Scope and Analytics Endpoints
  // ============================================================================

  /**
   * Get user activities across all accounts and profiles
   */
  async getUserActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const user = req.user;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Validate user ID format
      if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid user ID format'
        });
        return;
      }

      // Users can only access their own activities unless they're admin/superadmin
      if (user.role === RoleType.REGULAR_USER && user.id !== userId) {
        res.status(403).json({
          success: false,
          message: 'Access denied to user activities'
        });
        return;
      }

      let filters;
      try {
        filters = this.buildFiltersFromQuery(req.query);
      } catch (error) {
        res.status(400).json({
          success: false,
          message: error instanceof Error ? error.message : 'Invalid request parameters'
        });
        return;
      }

      const userObjectId = new mongoose.Types.ObjectId(userId);

      // Get both account and profile activities
      const [accountActivities, profileActivities] = await Promise.all([
        AccountActivityModel.find({ userId: userObjectId, ...filters }).limit(25).sort({ timestamp: -1 }),
        ProfileActivityModel.find({ userId: userObjectId, ...filters }).limit(25).sort({ timestamp: -1 })
      ]);

      // Combine and sort activities - simplified
      const allActivities = [
        ...accountActivities.map((a: any) => ({ 
          _id: a._id,
          activityType: a.activityType,
          timestamp: a.timestamp,
          scope: 'account'
        })),
        ...profileActivities.map((a: any) => ({ 
          _id: a._id,
          activityType: a.activityType,
          timestamp: a.timestamp,
          scope: 'profile'
        }))
      ].sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      res.json({
        success: true,
        data: allActivities,
        pagination: this.buildPagination(req.query, allActivities.length),
        summary: {
          totalActivities: allActivities.length,
          accountActivities: accountActivities.length,
          profileActivities: profileActivities.length
        }
      });

      // Log cross-scope access
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'VIEW_USER_ACTIVITIES',
        details: { targetUserId: userId }
      });

    } catch (error) {
      logger.error('Error fetching user activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch user activities'
      });
    }
  }

  /**
   * Get global activity insights (admin/superadmin only)
   */
  async getGlobalActivityInsights(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      
      if (!user || (user.role !== RoleType.SUPER_ADMIN && user.role !== RoleType.ADMIN_USER)) {
        res.status(403).json({
          success: false,
          message: 'Admin access required for global insights'
        });
        return;
      }

      const timeRange = this.buildTimeRange(req.query);
      
      // Get aggregated insights from both account and profile activities
      const [accountInsights, profileInsights] = await Promise.all([
        this.getAccountInsights(timeRange),
        this.getProfileInsights(timeRange)
      ]);

      const insights = {
        account: accountInsights,
        profile: profileInsights,
        combined: {
          totalActivities: accountInsights.totalActivities + profileInsights.totalActivities,
          totalMyPtsEarned: profileInsights.totalMyPtsEarned || 0,
          totalRewardableActivities: (accountInsights.rewardableActivities || 0) + (profileInsights.rewardableActivities || 0)
        }
      };

      res.json({
        success: true,
        data: insights,
        timeRange: timeRange ? {
          start: timeRange[0],
          end: timeRange[1]
        } : undefined
      });

      // Log global insights access
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'VIEW_GLOBAL_INSIGHTS',
        details: { role: user.role }
      });

    } catch (error) {
      logger.error('Error fetching global insights:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch global insights'
      });
    }
  }

  // ============================================================================
  // Role-Based Activity Endpoints
  // ============================================================================

  /**
   * Get admin global insights and system-wide activity analytics
   */
  async getAdminGlobalInsights(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      
      if (!user || user.role !== RoleType.SUPER_ADMIN && user.role !== RoleType.ADMIN_USER) {
        res.status(403).json({
          success: false,
          message: 'Superadmin access required for global insights'
        });
        return;
      }

      const timeRange = this.buildTimeRange(req.query);
      const { includeSystemActivities = true, includeUserMetrics = true } = req.query;

      // Get comprehensive global insights
      const [
        accountInsights,
        profileInsights,
        recentActivities,
        topActiveUsers,
        activityTrends,
        securityEvents
      ] = await Promise.all([
        this.getAccountInsights(timeRange),
        this.getProfileInsights(timeRange),
        this.getRecentGlobalActivities(50),
        this.getTopActiveUsers(10, timeRange),
        this.getActivityTrends(timeRange),
        this.getSecurityInsights(timeRange)
      ]);

      const insights = {
        overview: {
          totalActivities: accountInsights.totalActivities + profileInsights.totalActivities,
          totalUsers: accountInsights.uniqueUsers,
          totalProfiles: profileInsights.uniqueProfiles,
          totalMyPtsEarned: profileInsights.totalMyPtsEarned,
          rewardableActivities: accountInsights.rewardableActivities + profileInsights.rewardableActivities,
          systemGeneratedPercentage: (
            (accountInsights.systemActivities + profileInsights.systemActivities) /
            (accountInsights.totalActivities + profileInsights.totalActivities) * 100
          ).toFixed(2)
        },
        accountInsights,
        profileInsights,
        recentActivities: includeSystemActivities ? recentActivities : recentActivities.filter((a: any) => !a.isSystemGenerated),
        topActiveUsers: includeUserMetrics ? topActiveUsers : [],
        activityTrends,
        securityInsights: securityEvents,
        timeRange: timeRange ? {
          start: timeRange[0],
          end: timeRange[1]
        } : undefined,
        generatedAt: new Date()
      };

      res.json({
        success: true,
        data: insights
      });

      // Log admin access
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'VIEW_ADMIN_GLOBAL_INSIGHTS',
        details: {
          timeRange: timeRange ? { start: timeRange[0], end: timeRange[1] } : 'all',
          includeSystemActivities,
          includeUserMetrics
        }
      });

    } catch (error) {
      logger.error('Error fetching admin global insights:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch admin global insights'
      });
    }
  }

  /**
   * Get moderator user activities and insights
   */
  async getModeratorUserActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const { userId } = req.params;
      
      if (!user || (user.role !== RoleType.ADMIN_USER && user.role !== RoleType.SUPER_ADMIN)) {
        res.status(403).json({
          success: false,
          message: 'Admin or moderator access required'
        });
        return;
      }

      if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
        res.status(400).json({
          success: false,
          message: 'Valid user ID required'
        });
        return;
      }

      const timeRange = this.buildTimeRange(req.query);
      const { includeSensitiveData = false, flaggedOnly = false } = req.query;

      const userObjectId = new mongoose.Types.ObjectId(userId);

      // Get user activities and insights
      const [
        accountActivities,
        profileActivities,
        userStats,
        suspiciousActivities,
        recentConnections
      ] = await Promise.all([
        this.getUserAccountActivities(userObjectId, timeRange, flaggedOnly === 'true'),
        this.getUserProfileActivities(userObjectId, timeRange, flaggedOnly === 'true'),
        this.getUserActivityStats(userObjectId, timeRange),
        this.getSuspiciousUserActivities(userObjectId, timeRange),
        this.getUserRecentConnections(userObjectId, 20)
      ]);

      // Filter sensitive data based on role
      const filteredAccountActivities = includeSensitiveData === 'true' && user.role === RoleType.SUPER_ADMIN 
        ? accountActivities 
        : accountActivities.map(this.sanitizeActivityForModerator);

      const filteredProfileActivities = includeSensitiveData === 'true' && user.role === RoleType.SUPER_ADMIN
        ? profileActivities
        : profileActivities.map(this.sanitizeActivityForModerator);

      const insights = {
        userId,
        userStats,
        activities: {
          account: filteredAccountActivities,
          profile: filteredProfileActivities,
          total: accountActivities.length + profileActivities.length
        },
        moderationFlags: {
          suspiciousActivities,
          flaggedCount: suspiciousActivities.length,
          riskLevel: this.calculateUserRiskLevel(suspiciousActivities, userStats)
        },
        socialInsights: {
          recentConnections,
          connectionCount: recentConnections.length,
          networkActivity: await this.getUserNetworkActivity(userObjectId, timeRange)
        },
        timeRange: timeRange ? {
          start: timeRange[0],
          end: timeRange[1]
        } : undefined,
        generatedAt: new Date(),
        reviewedBy: {
          userId: user.id,
          role: user.role,
          email: user.email
        }
      };

      res.json({
        success: true,
        data: insights
      });

      // Log moderator access
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'MODERATOR_VIEW_USER_ACTIVITIES',
        details: {
          targetUserId: userId,
          includeSensitiveData,
          flaggedOnly,
          activitiesCount: insights.activities.total
        }
      });

    } catch (error) {
      logger.error('Error fetching moderator user activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch user activities for moderation'
      });
    }
  }

  /**
   * Get role-specific activity dashboard
   */
  async getRoleBasedActivityDashboard(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const timeRange = this.buildTimeRange(req.query);
      let dashboardData: any = {};

      switch (user.role) {
        case RoleType.SUPER_ADMIN:
          dashboardData = await this.getSuperadminDashboard(timeRange);
          break;
        case RoleType.ADMIN_USER:
          dashboardData = await this.getAdminDashboard(user.id, timeRange);
          break;
        case RoleType.REGULAR_USER:
          dashboardData = await this.getUserDashboard(user.id, timeRange);
          break;
        default:
          res.status(403).json({
            success: false,
            message: 'Invalid user role'
          });
          return;
      }

      res.json({
        success: true,
        data: {
          role: user.role,
          dashboard: dashboardData,
          timeRange: timeRange ? {
            start: timeRange[0],
            end: timeRange[1]
          } : undefined,
          generatedAt: new Date()
        }
      });

    } catch (error) {
      logger.error('Error fetching role-based dashboard:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch role-based dashboard'
      });
    }
  }

  // ============================================================================
  // Admin and Maintenance Endpoints
  // ============================================================================

  /**
   * Cleanup old activities (admin/superadmin only)
   */
  async cleanupOldActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      
      if (!user || (user.role !== RoleType.ADMIN_USER && user.role !== RoleType.SUPER_ADMIN)) {
        res.status(403).json({
          success: false,
          message: 'Admin access required for cleanup operations'
        });
        return;
      }

      const { olderThanDays = 90 } = req.body;
      const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

      const [accountCleanup, profileCleanup] = await Promise.all([
        AccountActivityModel.deleteMany({ timestamp: { $lt: cutoffDate } }),
        ProfileActivityModel.deleteMany({ timestamp: { $lt: cutoffDate } })
      ]);

      const totalDeleted = accountCleanup.deletedCount + profileCleanup.deletedCount;

      res.json({
        success: true,
        message: `Cleaned up ${totalDeleted} old activities`,
        details: {
          accountActivitiesDeleted: accountCleanup.deletedCount,
          profileActivitiesDeleted: profileCleanup.deletedCount,
          olderThanDays
        }
      });

      // Log cleanup operation
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'CLEANUP_OLD_ACTIVITIES',
        details: {
          deletedCount: totalDeleted,
          olderThanDays
        }
      });

    } catch (error) {
      logger.error('Error cleaning up activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cleanup old activities'
      });
    }
  }

  /**
   * Export activities with comprehensive filtering and GDPR compliance
   */
  async exportActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      
      if (!user || (user.role !== RoleType.ADMIN_USER && user.role !== RoleType.SUPER_ADMIN)) {
        res.status(403).json({
          success: false,
          message: 'Admin access required for export operations'
        });
        return;
      }

      const { 
        format = 'json', 
        includePersonalData = false, 
        includeMetadata = true,
        columns 
      } = req.body;
      
      let filters;
      try {
        filters = this.buildFiltersFromQuery(req.query);
      } catch (error) {
        res.status(400).json({
          success: false,
          message: error instanceof Error ? error.message : 'Invalid request parameters'
        });
        return;
      }

      // Import the export service
      const { activityExportService } = await import('../services/activity-export.service');

      // Configure export
      const exportConfig = {
        format,
        filters,
        includePersonalData,
        includeMetadata,
        columns
      };

      // Start export process
      const exportResult = await activityExportService.exportActivities(
        exportConfig,
        new mongoose.Types.ObjectId(user.id),
        user.role
      );

      if (exportResult.success) {
        res.status(201).json({
          success: true,
          message: 'Export completed successfully',
          export: {
            exportId: exportResult.exportId,
            fileName: exportResult.fileName,
            fileSize: exportResult.fileSize,
            recordsExported: exportResult.recordsExported,
            format: exportResult.exportFormat,
            downloadUrl: exportResult.downloadUrl,
            expiresAt: exportResult.expiresAt
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Export failed',
          errors: exportResult.errors
        });
      }

      // Log export request
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'EXPORT_ACTIVITIES',
        details: { 
          format, 
          includePersonalData, 
          recordsExported: exportResult.recordsExported,
          success: exportResult.success
        }
      });

    } catch (error) {
      logger.error('Error exporting activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to export activities'
      });
    }
  }

  /**
   * Export user data for GDPR compliance
   */
  async exportUserDataGDPR(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const { userId } = req.params;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Validate access permissions
      if (user.role === RoleType.REGULAR_USER && user.id !== userId) {
        res.status(403).json({
          success: false,
          message: 'Users can only export their own data'
        });
        return;
      }

      if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
        res.status(400).json({
          success: false,
          message: 'Valid user ID required'
        });
        return;
      }

      const { format = 'json', includePersonalData = true } = req.body;

      // Import the export service
      const { activityExportService } = await import('../services/activity-export.service');

      // Start GDPR export
      const exportResult = await activityExportService.exportUserDataGDPR(
        new mongoose.Types.ObjectId(userId),
        format,
        includePersonalData
      );

      if (exportResult.success) {
        res.status(201).json({
          success: true,
          message: 'GDPR export completed successfully',
          export: {
            exportId: exportResult.exportId,
            fileName: exportResult.fileName,
            fileSize: exportResult.fileSize,
            recordsExported: exportResult.recordsExported,
            format: exportResult.exportFormat,
            downloadUrl: exportResult.downloadUrl,
            expiresAt: exportResult.expiresAt
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'GDPR export failed',
          errors: exportResult.errors
        });
      }

      // Log GDPR export
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'GDPR_DATA_EXPORT',
        details: { 
          targetUserId: userId,
          format,
          includePersonalData,
          recordsExported: exportResult.recordsExported,
          success: exportResult.success
        }
      });

    } catch (error: any) {
      logger.error('Error exporting user data for GDPR:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to export user data'
      });
    }
  }

  /**
   * Get export progress
   */
  async getExportProgress(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const { exportId } = req.params;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Import the export service
      const { activityExportService } = await import('../services/activity-export.service');

      const progress = activityExportService.getExportProgress(exportId!);

      if (!progress) {
        res.status(404).json({
          success: false,
          message: 'Export not found or completed'
        });
        return;
      }

      res.json({
        success: true,
        data: progress
      });

    } catch (error: any) {
      logger.error('Error getting export progress:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get export progress'
      });
    }
  }

  /**
   * Download exported file
   */
  async downloadExport(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const { exportId } = req.params;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Import the export service
      const { activityExportService } = await import('../services/activity-export.service');

      const downloadInfo = await activityExportService.downloadExport(exportId!);

      if (!downloadInfo) {
        res.status(404).json({
          success: false,
          message: 'Export file not found or expired'
        });
        return;
      }

      // Set appropriate headers for file download
      res.setHeader('Content-Disposition', `attachment; filename="${downloadInfo.fileName}"`);
      res.setHeader('Content-Type', this.getContentType(downloadInfo.fileName));

      // Stream the file
      const fs = await import('fs');
      const fileStream = fs.default.createReadStream(downloadInfo.filePath);
      fileStream.pipe(res);

      // Log download
      await auditLogService.logRequest({
        timestamp: new Date(),
        userId: user.id,
        action: 'DOWNLOAD_EXPORT',
        details: { 
          exportId,
          fileName: downloadInfo.fileName
        }
      });

    } catch (error: any) {
      logger.error('Error downloading export:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to download export'
      });
    }
  }

  private getContentType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'json': 'application/json',
      'csv': 'text/csv',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };
    
    return mimeTypes[extension || ''] || 'application/octet-stream';
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private canAccessAccount(user: ActivityRequestUser, accountId: string): boolean {
    if (user.role === RoleType.SUPER_ADMIN || user.role === RoleType.ADMIN_USER) return true;

    // Users can only access their own account activities
    return user.id === accountId || user._id === accountId;
  }

  private async buildDatabaseQuery(targetId: mongoose.Types.ObjectId, filters: IActivityFilter, scope: 'account' | 'profile'): Promise<any> {
    const query: any = {};
    
    // Set the target field based on scope
    if (scope === 'account') {
      query.accountId = targetId;
    } else {
      query.profileId = targetId;
    }

    // Time range filters
    if (filters.startDate || filters.endDate || filters.timeRange) {
      query.timestamp = {};
      
      if (filters.startDate && filters.endDate) {
        query.timestamp.$gte = filters.startDate;
        query.timestamp.$lte = filters.endDate;
      } else if (filters.timeRange) {
        const timeRange = this.buildTimeRange({ timeRange: filters.timeRange });
        if (timeRange) {
          query.timestamp.$gte = timeRange[0];
          query.timestamp.$lte = timeRange[1];
        }
      } else {
        if (filters.startDate) query.timestamp.$gte = filters.startDate;
        if (filters.endDate) query.timestamp.$lte = filters.endDate;
      }
    }

    // Activity type filters
    if (filters.activityTypes && filters.activityTypes.length > 0) {
      query.activityType = { $in: filters.activityTypes };
    }

    // Category filters
    if (filters.categories && filters.categories.length > 0) {
      query.category = { $in: filters.categories };
    }

    // Boolean filters
    if (filters.isRewardable !== undefined) {
      query.isRewardable = filters.isRewardable;
    }

    if (filters.isBonusable !== undefined) {
      query.isBonusable = filters.isBonusable;
    }

    if (filters.isSystemGenerated !== undefined) {
      query.isSystemGenerated = filters.isSystemGenerated;
    }

    // Profile-specific filters
    if (scope === 'profile') {
      if (filters.hasMyPtsEarned !== undefined) {
        if (filters.hasMyPtsEarned) {
          query.myPtsEarned = { $gt: 0 };
        } else {
          query.$or = [
            { myPtsEarned: { $exists: false } },
            { myPtsEarned: { $lte: 0 } }
          ];
        }
      }

      if (filters.hasMyPtsSpent !== undefined) {
        if (filters.hasMyPtsSpent) {
          query.myPtsSpent = { $gt: 0 };
        } else {
          query.$or = [
            { myPtsSpent: { $exists: false } },
            { myPtsSpent: { $lte: 0 } }
          ];
        }
      }
    }

    // User role filters
    if (filters.userRoles && filters.userRoles.length > 0) {
      query.userRole = { $in: filters.userRoles };
    }

    // Profile ID filters (for account activities)
    if (scope === 'account' && filters.profileIds && filters.profileIds.length > 0) {
      query.profileId = { $in: filters.profileIds };
    }

    logger.debug(`Built ${scope} database query`, {
      query: JSON.stringify(query),
      filters,
      targetId: targetId.toString()
    });

    return query;
  }

  private async canAccessProfile(user: ActivityRequestUser, profileId: string): Promise<boolean> {
    if (user.role === RoleType.SUPER_ADMIN || user.role === RoleType.ADMIN_USER) return true;
    
    try {
      // Check if the user owns this profile or has access to the associated account
      const ProfileModel = require('../models/profile.model');
      const profile = await ProfileModel.findById(profileId);
      
      if (!profile) return false;
      
      // Check if the user owns the profile or the associated account
      return profile.userId?.toString() === user.id || 
             profile.accountId?.toString() === user.id;
    } catch (error) {
      console.error('Error checking profile access:', error);
      return false;
    }
  }

  private buildFiltersFromQuery(query: any): IActivityFilter {
    const filters: IActivityFilter = {};

    // Time filters with validation
    if (query.startDate) {
      const startDate = new Date(query.startDate);
      if (isNaN(startDate.getTime())) {
        throw new Error('Invalid start date format');
      }
      filters.startDate = startDate;
    }
    if (query.endDate) {
      const endDate = new Date(query.endDate);
      if (isNaN(endDate.getTime())) {
        throw new Error('Invalid end date format');
      }
      filters.endDate = endDate;
    }
    if (query.timeRange) {
      const validTimeRanges = ['today', 'week', 'month', 'quarter', 'year', 'custom'];
      if (!validTimeRanges.includes(query.timeRange)) {
        throw new Error('Invalid time range');
      }
      filters.timeRange = query.timeRange;
    }

    // Activity filters
    if (query.activityTypes) {
      filters.activityTypes = Array.isArray(query.activityTypes) 
        ? query.activityTypes 
        : [query.activityTypes];
    }
    if (query.categories) {
      filters.categories = Array.isArray(query.categories) 
        ? query.categories 
        : [query.categories];
    }

    // Boolean filters
    if (query.isRewardable !== undefined) filters.isRewardable = query.isRewardable === 'true';
    if (query.isBonusable !== undefined) filters.isBonusable = query.isBonusable === 'true';
    if (query.isSystemGenerated !== undefined) filters.isSystemGenerated = query.isSystemGenerated === 'true';

    // Pagination with validation
    if (query.page) {
      const page = parseInt(query.page);
      if (isNaN(page) || page < 1) {
        throw new Error('Page must be a positive integer');
      }
      filters.page = page;
    }
    if (query.limit) {
      const limit = parseInt(query.limit);
      if (isNaN(limit) || limit < 1 || limit > 500) {
        throw new Error('Limit must be between 1 and 500');
      }
      filters.limit = limit;
    }
    if (query.sortBy) {
      const validSortFields = ['timestamp', 'activityType', 'category', 'myPtsEarned'];
      if (!validSortFields.includes(query.sortBy)) {
        throw new Error('Invalid sort field');
      }
      filters.sortBy = query.sortBy;
    }
    if (query.sortOrder) {
      if (!['asc', 'desc'].includes(query.sortOrder)) {
        throw new Error('Sort order must be asc or desc');
      }
      filters.sortOrder = query.sortOrder;
    }

    return filters;
  }

  private buildTimeRange(query: any): Date[] | undefined {
    if (query.startDate && query.endDate) {
      return [new Date(query.startDate), new Date(query.endDate)];
    }
    
    if (query.timeRange) {
      const now = new Date();
      const ranges: Record<string, Date[]> = {
        today: [new Date(now.getFullYear(), now.getMonth(), now.getDate()), now],
        week: [new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now],
        month: [new Date(now.getFullYear(), now.getMonth(), 1), now],
        quarter: [new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1), now],
        year: [new Date(now.getFullYear(), 0, 1), now]
      };
      
      return ranges[query.timeRange];
    }

    return undefined;
  }

  private buildPagination(query: any, resultCount: number) {
    const page = parseInt(query.page) || 1;
    const limit = Math.min(parseInt(query.limit) || 50, 500);
    const total = resultCount; // In real implementation, would be total count from DB

    return {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1
    };
  }

  private filterAnalyticsByRole(analytics: any, role: string): any {
    // Remove sensitive analytics data based on role
    if (role === RoleType.REGULAR_USER) {
      const {
        systemActivities,
        adminActivities,
        sensitiveMetrics,
        ...safeAnalytics
      } = analytics;
      return safeAnalytics;
    }
    return analytics;
  }

  private async getAccountInsights(timeRange?: Date[]): Promise<any> {
    try {
      let matchQuery: any = {};
      if (timeRange && timeRange.length === 2) {
        matchQuery.timestamp = {
          $gte: timeRange[0],
          $lte: timeRange[1]
        };
      }

      const result = await AccountActivityModel.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: null,
            totalActivities: { $sum: 1 },
            rewardableActivities: { $sum: { $cond: ['$isRewardable', 1, 0] } },
            systemActivities: { $sum: { $cond: ['$isSystemGenerated', 1, 0] } },
            uniqueAccounts: { $addToSet: '$accountId' },
            uniqueUsers: { $addToSet: '$userId' }
          }
        },
        {
          $project: {
            totalActivities: 1,
            rewardableActivities: 1,
            systemActivities: 1,
            uniqueAccounts: { $size: '$uniqueAccounts' },
            uniqueUsers: { $size: '$uniqueUsers' }
          }
        }
      ]);

      return result[0] || {
        totalActivities: 0,
        rewardableActivities: 0,
        systemActivities: 0,
        uniqueAccounts: 0,
        uniqueUsers: 0
      };
    } catch (error) {
      logger.error('Error getting account insights:', error);
      return {
        totalActivities: 0,
        rewardableActivities: 0,
        systemActivities: 0,
        uniqueAccounts: 0,
        uniqueUsers: 0
      };
    }
  }

  private async getProfileInsights(timeRange?: Date[]): Promise<any> {
    try {
      let matchQuery: any = {};
      if (timeRange && timeRange.length === 2) {
        matchQuery.timestamp = {
          $gte: timeRange[0],
          $lte: timeRange[1]
        };
      }

      const result = await ProfileActivityModel.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: null,
            totalActivities: { $sum: 1 },
            rewardableActivities: { $sum: { $cond: ['$isRewardable', 1, 0] } },
            systemActivities: { $sum: { $cond: ['$isSystemGenerated', 1, 0] } },
            totalMyPtsEarned: { $sum: '$myPtsEarned' },
            totalMyPtsSpent: { $sum: '$myPtsSpent' },
            uniqueProfiles: { $addToSet: '$profileId' },
            uniqueUsers: { $addToSet: '$userId' }
          }
        },
        {
          $project: {
            totalActivities: 1,
            rewardableActivities: 1,
            systemActivities: 1,
            totalMyPtsEarned: 1,
            totalMyPtsSpent: 1,
            myPtsNet: { $subtract: ['$totalMyPtsEarned', '$totalMyPtsSpent'] },
            uniqueProfiles: { $size: '$uniqueProfiles' },
            uniqueUsers: { $size: '$uniqueUsers' }
          }
        }
      ]);

      return result[0] || {
        totalActivities: 0,
        rewardableActivities: 0,
        systemActivities: 0,
        totalMyPtsEarned: 0,
        totalMyPtsSpent: 0,
        myPtsNet: 0,
        uniqueProfiles: 0,
        uniqueUsers: 0
      };
    } catch (error) {
      logger.error('Error getting profile insights:', error);
      return {
        totalActivities: 0,
        rewardableActivities: 0,
        systemActivities: 0,
        totalMyPtsEarned: 0,
        totalMyPtsSpent: 0,
        myPtsNet: 0,
        uniqueProfiles: 0,
        uniqueUsers: 0
      };
    }
  }

  // ============================================================================
  // Role-Based Dashboard Helper Methods
  // ============================================================================

  private async getSuperadminDashboard(timeRange?: Date[]): Promise<any> {
    try {
      const [
        globalStats,
        recentActivities,
        securityAlerts,
        systemHealth,
        userGrowth
      ] = await Promise.all([
        this.getGlobalSystemStats(timeRange),
        this.getRecentGlobalActivities(20),
        this.getSecurityAlerts(timeRange),
        this.getSystemHealthMetrics(),
        this.getUserGrowthMetrics(timeRange)
      ]);

      return {
        globalStats,
        recentActivities,
        securityAlerts,
        systemHealth,
        userGrowth,
        alerts: await this.getSystemAlerts()
      };
    } catch (error) {
      logger.error('Error getting superadmin dashboard:', error);
      return {};
    }
  }

  private async getAdminDashboard(adminId: string, timeRange?: Date[]): Promise<any> {
    try {
      const [
        managedUsersStats,
        moderationQueue,
        recentReports,
        activitySummary
      ] = await Promise.all([
        this.getManagedUsersStats(adminId, timeRange),
        this.getModerationQueue(adminId),
        this.getRecentReports(adminId, 10),
        this.getAdminActivitySummary(adminId, timeRange)
      ]);

      return {
        managedUsersStats,
        moderationQueue,
        recentReports,
        activitySummary,
        permissions: await this.getAdminPermissions(adminId)
      };
    } catch (error) {
      logger.error('Error getting admin dashboard:', error);
      return {};
    }
  }

  private async getUserDashboard(userId: string, timeRange?: Date[]): Promise<any> {
    try {
      const userObjectId = new mongoose.Types.ObjectId(userId);
      
      const [
        userStats,
        recentActivities,
        myPtsBreakdown,
        achievementProgress
      ] = await Promise.all([
        this.getUserActivityStats(userObjectId, timeRange),
        this.getUserRecentActivities(userObjectId, 15),
        this.getUserMyPtsBreakdown(userObjectId, timeRange),
        this.getUserAchievementProgress(userObjectId)
      ]);

      return {
        userStats,
        recentActivities,
        myPtsBreakdown,
        achievementProgress,
        streaks: await this.getUserStreaks(userObjectId)
      };
    } catch (error) {
      logger.error('Error getting user dashboard:', error);
      return {};
    }
  }

  // ============================================================================
  // Supporting Helper Methods
  // ============================================================================

  private async getRecentGlobalActivities(limit: number): Promise<any[]> {
    try {
      const [accountActivities, profileActivities] = await Promise.all([
        AccountActivityModel.find({})
          .sort({ timestamp: -1 })
          .limit(Math.ceil(limit / 2))
          .populate('userId', 'email fullName')
          .lean(),
        ProfileActivityModel.find({})
          .sort({ timestamp: -1 })
          .limit(Math.ceil(limit / 2))
          .populate('userId', 'email fullName')
          .populate('profileId', 'profileInformation.name')
          .lean()
      ]);

      const combined = [
        ...accountActivities.map((a: any) => ({ ...a, scope: 'account' })),
        ...profileActivities.map((a: any) => ({ ...a, scope: 'profile' }))
      ];

      return combined
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      logger.error('Error getting recent global activities:', error);
      return [];
    }
  }

  private async getTopActiveUsers(limit: number, timeRange?: Date[]): Promise<any[]> {
    try {
      let matchStage: any = {};
      if (timeRange && timeRange.length === 2) {
        matchStage.timestamp = { $gte: timeRange[0], $lte: timeRange[1] };
      }

      const pipeline = [
        { $match: matchStage },
        {
          $group: {
            _id: '$userId',
            activityCount: { $sum: 1 },
            myPtsEarned: { $sum: '$myPtsEarned' },
            lastActivity: { $max: '$timestamp' },
            activityTypes: { $addToSet: '$activityType' }
          }
        },
        { $sort: { activityCount: -1 } },
        { $limit: limit },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: RoleType.REGULAR_USER
          }
        },
        { $unwind: '$user' },
        {
          $project: {
            userId: '$_id',
            activityCount: 1,
            myPtsEarned: 1,
            lastActivity: 1,
            activityTypeCount: { $size: '$activityTypes' },
            userName: '$user.fullName',
            userEmail: '$user.email'
          }
        }
      ];

      return await ProfileActivityModel.aggregate(pipeline as any);
    } catch (error) {
      logger.error('Error getting top active users:', error);
      return [];
    }
  }

  private async getActivityTrends(timeRange?: Date[]): Promise<any> {
    try {
      const days = timeRange 
        ? Math.ceil((timeRange[1].getTime() - timeRange[0].getTime()) / (1000 * 60 * 60 * 24))
        : 30;

      let matchStage: any = {};
      if (timeRange && timeRange.length === 2) {
        matchStage.timestamp = { $gte: timeRange[0], $lte: timeRange[1] };
      } else {
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        matchStage.timestamp = { $gte: thirtyDaysAgo };
      }

      const pipeline = [
        { $match: matchStage },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
              activityType: '$activityType'
            },
            count: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: '$_id.date',
            activities: {
              $push: {
                type: '$_id.activityType',
                count: '$count'
              }
            },
            totalCount: { $sum: '$count' }
          }
        },
        { $sort: { _id: 1 } }
      ];

      const trends = await ProfileActivityModel.aggregate(pipeline as any);
      
      return {
        dailyTrends: trends,
        averageDaily: trends.length > 0 
          ? trends.reduce((sum, day) => sum + day.totalCount, 0) / trends.length 
          : 0,
        peakDay: trends.length > 0 
          ? trends.reduce((peak, day) => day.totalCount > peak.totalCount ? day : peak)
          : null
      };
    } catch (error) {
      logger.error('Error getting activity trends:', error);
      return { dailyTrends: [], averageDaily: 0, peakDay: null };
    }
  }

  private async getSecurityInsights(timeRange?: Date[]): Promise<any> {
    try {
      let matchStage: any = {
        category: { $in: ['ACCOUNT_SECURITY', 'SECURITY_EVENTS'] }
      };
      
      if (timeRange && timeRange.length === 2) {
        matchStage.timestamp = { $gte: timeRange[0], $lte: timeRange[1] };
      }

      const securityEvents = await AccountActivityModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$activityType',
            count: { $sum: 1 },
            recentEvent: { $max: '$timestamp' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      return {
        securityEvents,
        totalSecurityEvents: securityEvents.reduce((sum, event) => sum + event.count, 0),
        criticalEvents: securityEvents.filter(event => 
          ['SECURITY_ALERT_GENERATED', 'SUSPICIOUS_LOGIN', 'ACCOUNT_LOCKED'].includes(event._id)
        )
      };
    } catch (error) {
      logger.error('Error getting security insights:', error);
      return { securityEvents: [], totalSecurityEvents: 0, criticalEvents: [] };
    }
  }

  private async getUserAccountActivities(
    userId: mongoose.Types.ObjectId,
    timeRange?: Date[],
    flaggedOnly: boolean = false
  ): Promise<any[]> {
    try {
      let query: any = { userId };
      
      if (timeRange && timeRange.length === 2) {
        query.timestamp = { $gte: timeRange[0], $lte: timeRange[1] };
      }

      if (flaggedOnly) {
        query.$or = [
          { 'metadata.flagged': true },
          { 'metadata.suspicious': true },
          { activityType: { $in: ['SECURITY_ALERT_GENERATED', 'SUSPICIOUS_LOGIN'] } }
        ];
      }

      return await AccountActivityModel.find(query)
        .sort({ timestamp: -1 })
        .limit(100)
        .lean();
    } catch (error) {
      logger.error('Error getting user account activities:', error);
      return [];
    }
  }

  private async getUserProfileActivities(
    userId: mongoose.Types.ObjectId,
    timeRange?: Date[],
    flaggedOnly: boolean = false
  ): Promise<any[]> {
    try {
      let query: any = { userId };
      
      if (timeRange && timeRange.length === 2) {
        query.timestamp = { $gte: timeRange[0], $lte: timeRange[1] };
      }

      if (flaggedOnly) {
        query.$or = [
          { 'metadata.flagged': true },
          { 'metadata.suspicious': true }
        ];
      }

      return await ProfileActivityModel.find(query)
        .sort({ timestamp: -1 })
        .limit(100)
        .populate('profileId', 'profileInformation.name')
        .lean();
    } catch (error) {
      logger.error('Error getting user profile activities:', error);
      return [];
    }
  }

  private async getUserActivityStats(
    userId: mongoose.Types.ObjectId,
    timeRange?: Date[]
  ): Promise<any> {
    try {
      const [accountStats, profileStats] = await Promise.all([
        AccountActivityModel.aggregate([
          {
            $match: {
              userId,
              ...(timeRange && timeRange.length === 2 ? {
                timestamp: { $gte: timeRange[0], $lte: timeRange[1] }
              } : {})
            }
          },
          {
            $group: {
              _id: null,
              totalActivities: { $sum: 1 },
              categories: { $addToSet: '$category' },
              recentActivity: { $max: '$timestamp' }
            }
          }
        ]),
        ProfileActivityModel.aggregate([
          {
            $match: {
              userId,
              ...(timeRange && timeRange.length === 2 ? {
                timestamp: { $gte: timeRange[0], $lte: timeRange[1] }
              } : {})
            }
          },
          {
            $group: {
              _id: null,
              totalActivities: { $sum: 1 },
              totalMyPtsEarned: { $sum: '$myPtsEarned' },
              totalMyPtsSpent: { $sum: '$myPtsSpent' },
              categories: { $addToSet: '$category' },
              recentActivity: { $max: '$timestamp' }
            }
          }
        ])
      ]);

      const accountData = accountStats[0] || { totalActivities: 0, categories: [], recentActivity: null };
      const profileData = profileStats[0] || { 
        totalActivities: 0, 
        totalMyPtsEarned: 0, 
        totalMyPtsSpent: 0, 
        categories: [], 
        recentActivity: null 
      };

      return {
        totalActivities: accountData.totalActivities + profileData.totalActivities,
        accountActivities: accountData.totalActivities,
        profileActivities: profileData.totalActivities,
        myPtsEarned: profileData.totalMyPtsEarned || 0,
        myPtsSpent: profileData.totalMyPtsSpent || 0,
        myPtsBalance: (profileData.totalMyPtsEarned || 0) - (profileData.totalMyPtsSpent || 0),
        categoriesUsed: [...new Set([...accountData.categories, ...profileData.categories])],
        lastActivity: accountData.recentActivity > profileData.recentActivity 
          ? accountData.recentActivity 
          : profileData.recentActivity
      };
    } catch (error) {
      logger.error('Error getting user activity stats:', error);
      return {
        totalActivities: 0,
        accountActivities: 0,
        profileActivities: 0,
        myPtsEarned: 0,
        myPtsSpent: 0,
        myPtsBalance: 0,
        categoriesUsed: [],
        lastActivity: null
      };
    }
  }

  private sanitizeActivityForModerator(activity: any): any {
    // Remove sensitive fields for moderator view
    const sanitized = { ...activity };
    delete sanitized.ipAddress;
    delete sanitized.deviceFingerprint;
    delete sanitized.sessionId;
    
    // Sanitize metadata
    if (sanitized.metadata) {
      const { password, token, secret, ...safeMetadata } = sanitized.metadata;
      sanitized.metadata = safeMetadata;
    }
    
    return sanitized;
  }

  private calculateUserRiskLevel(suspiciousActivities: any[], userStats: any): string {
    const riskFactors = {
      high: suspiciousActivities.length > 10,
      medium: suspiciousActivities.length > 5,
      recentSuspicious: suspiciousActivities.some(a => 
        new Date(a.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
      ),
      highActivity: userStats.totalActivities > 1000
    };

    if (riskFactors.high || riskFactors.recentSuspicious) {
      return 'HIGH';
    } else if (riskFactors.medium) {
      return 'MEDIUM';
    } else {
      return 'LOW';
    }
  }

  private async getSuspiciousUserActivities(
    userId: mongoose.Types.ObjectId,
    timeRange?: Date[]
  ): Promise<any[]> {
    try {
      let query: any = {
        userId,
        $or: [
          { 'metadata.suspicious': true },
          { 'metadata.flagged': true },
          { activityType: { $in: ['SECURITY_ALERT_GENERATED', 'SUSPICIOUS_LOGIN'] } }
        ]
      };

      if (timeRange && timeRange.length === 2) {
        query.timestamp = { $gte: timeRange[0], $lte: timeRange[1] };
      }

      const [accountSuspicious, profileSuspicious] = await Promise.all([
        AccountActivityModel.find(query).sort({ timestamp: -1 }).limit(50).lean(),
        ProfileActivityModel.find(query).sort({ timestamp: -1 }).limit(50).lean()
      ]);

      return [...accountSuspicious, ...profileSuspicious]
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      logger.error('Error getting suspicious user activities:', error);
      return [];
    }
  }

  private async getUserRecentConnections(
    userId: mongoose.Types.ObjectId,
    limit: number
  ): Promise<any[]> {
    try {
      return await ProfileActivityModel.find({
        userId,
        category: 'social_interactions',
        activityType: { $in: ['CONNECTION_MADE', 'CONTACT_EXCHANGE', 'BUSINESS_CARD_SHARED'] }
      })
      .sort({ timestamp: -1 })
      .limit(limit)
      .populate('relatedProfileId', 'profileInformation.name profileInformation.username')
      .lean();
    } catch (error) {
      logger.error('Error getting user recent connections:', error);
      return [];
    }
  }

  private async getUserNetworkActivity(
    userId: mongoose.Types.ObjectId,
    timeRange?: Date[]
  ): Promise<any> {
    try {
      let matchStage: any = {
        userId,
        category: 'social_interactions'
      };

      if (timeRange && timeRange.length === 2) {
        matchStage.timestamp = { $gte: timeRange[0], $lte: timeRange[1] };
      }

      const networkStats = await ProfileActivityModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$activityType',
            count: { $sum: 1 },
            uniqueProfiles: { $addToSet: '$relatedProfileId' }
          }
        }
      ]);

      return {
        networkStats,
        totalNetworkActivities: networkStats.reduce((sum, stat) => sum + stat.count, 0),
        uniqueConnectionsCount: new Set(
          networkStats.flatMap(stat => stat.uniqueProfiles.filter(Boolean))
        ).size
      };
    } catch (error) {
      logger.error('Error getting user network activity:', error);
      return { networkStats: [], totalNetworkActivities: 0, uniqueConnectionsCount: 0 };
    }
  }

  // Additional helper methods (simplified implementations)
  private async getGlobalSystemStats(timeRange?: Date[]): Promise<any> {
    // Implementation for global system statistics
    return { totalUsers: 0, totalActivities: 0, systemLoad: 'normal' };
  }

  private async getSecurityAlerts(timeRange?: Date[]): Promise<any[]> {
    // Implementation for security alerts
    return [];
  }

  private async getSystemHealthMetrics(): Promise<any> {
    // Implementation for system health metrics
    return { status: 'healthy', uptime: '99.9%' };
  }

  private async getUserGrowthMetrics(timeRange?: Date[]): Promise<any> {
    // Implementation for user growth metrics
    return { newUsers: 0, activeUsers: 0, growthRate: 0 };
  }

  private async getSystemAlerts(): Promise<any[]> {
    // Implementation for system alerts
    return [];
  }

  private async getManagedUsersStats(adminId: string, timeRange?: Date[]): Promise<any> {
    // Implementation for managed users statistics
    return { totalManagedUsers: 0, activeUsers: 0 };
  }

  private async getModerationQueue(adminId: string): Promise<any[]> {
    // Implementation for moderation queue
    return [];
  }

  private async getRecentReports(adminId: string, limit: number): Promise<any[]> {
    // Implementation for recent reports
    return [];
  }

  private async getAdminActivitySummary(adminId: string, timeRange?: Date[]): Promise<any> {
    // Implementation for admin activity summary
    return { actionsPerformed: 0, resolvedIssues: 0 };
  }

  private async getAdminPermissions(adminId: string): Promise<any> {
    // Implementation for admin permissions
    return { canModerate: true, canManageUsers: true };
  }

  private async getUserRecentActivities(userId: mongoose.Types.ObjectId, limit: number): Promise<any[]> {
    // Implementation for user recent activities
    return [];
  }

  private async getUserMyPtsBreakdown(userId: mongoose.Types.ObjectId, timeRange?: Date[]): Promise<any> {
    // Implementation for user MyPts breakdown
    return { earned: 0, spent: 0, balance: 0 };
  }

  private async getUserAchievementProgress(userId: mongoose.Types.ObjectId): Promise<any> {
    // Implementation for user achievement progress
    return { achievements: [], progress: 0 };
  }

  private async getUserStreaks(userId: mongoose.Types.ObjectId): Promise<any> {
    // Implementation for user streaks
    return { currentStreak: 0, longestStreak: 0 };
  }

  // ============================================================================
  // Advanced Search and Analytics Endpoints
  // ============================================================================

  /**
   * Advanced activity search with filtering and facets
   */
  async advancedSearch(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Build search context
      const context = {
        userId: new mongoose.Types.ObjectId(user.id),
        userRole: user.role,
        allowedVisibility: this.getAllowedVisibility(user.role)
      };

      // Build filters from query
      const filters = this.buildFiltersFromQuery(req.query);

      // Build search options
      const searchOptions = {
        textSearch: req.query.search as string,
        fuzzySearch: req.query.fuzzy === 'true',
        searchFields: req.query.searchFields 
          ? (Array.isArray(req.query.searchFields) ? req.query.searchFields : [req.query.searchFields])
          : undefined,
        caseSensitive: req.query.caseSensitive === 'true',
        exactMatch: req.query.exactMatch === 'true'
      };

      // Build pagination options
      const paginationOptions = {
        page: parseInt(req.query.page as string) || 1,
        limit: Math.min(parseInt(req.query.limit as string) || 20, 500),
        sortBy: req.query.sortBy as string || 'timestamp',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc'
      };

      const results = await activitySearchService.searchActivities(
        filters,
        searchOptions,
        paginationOptions,
        context
      );

      res.status(200).json({
        success: true,
        data: results.activities,
        pagination: {
          currentPage: results.currentPage,
          totalPages: results.totalPages,
          totalCount: results.totalCount,
          hasMore: results.hasMore
        },
        facets: results.facets,
        searchMetrics: results.searchMetrics
      });

    } catch (error) {
      logger.error('Error in advanced search:', error);
      res.status(500).json({
        success: false,
        message: 'Search failed'
      });
    }
  }

  /**
   * Quick search for real-time suggestions
   */
  async quickSearch(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const searchTerm = req.query.q as string;
      if (!searchTerm || searchTerm.length < 2) {
        res.status(400).json({
          success: false,
          message: 'Search term must be at least 2 characters'
        });
        return;
      }

      const context = {
        userId: new mongoose.Types.ObjectId(user.id),
        userRole: user.role,
        allowedVisibility: this.getAllowedVisibility(user.role)
      };

      const limit = parseInt(req.query.limit as string) || 10;
      const results = await activitySearchService.quickSearch(searchTerm, context, limit);

      res.status(200).json({
        success: true,
        data: results.data,
        totalCount: results.totalCount
      });

    } catch (error) {
      logger.error('Error in quick search:', error);
      res.status(500).json({
        success: false,
        message: 'Quick search failed'
      });
    }
  }

  /**
   * Find similar activities
   */
  async findSimilarActivities(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const { activityId } = req.params;
      if (!activityId || !mongoose.Types.ObjectId.isValid(activityId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid activity ID'
        });
        return;
      }

      const context = {
        userId: new mongoose.Types.ObjectId(user.id),
        userRole: user.role,
        allowedVisibility: this.getAllowedVisibility(user.role)
      };

      const limit = parseInt(req.query.limit as string) || 20;
      const results = await activitySearchService.findSimilarActivities(activityId, context, limit);

      res.status(200).json({
        success: true,
        data: results.data,
        pagination: {
          currentPage: results.currentPage,
          totalPages: results.totalPages,
          totalCount: results.totalCount,
          hasMore: results.hasMore
        }
      });

    } catch (error) {
      logger.error('Error finding similar activities:', error);
      res.status(500).json({
        success: false,
        message: 'Similar activities search failed'
      });
    }
  }

  /**
   * Generate comprehensive analytics report
   */
  async generateAnalyticsReport(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Build time range
      const timeRange = this.buildAnalyticsTimeRange(req.query);
      if (!timeRange) {
        res.status(400).json({
          success: false,
          message: 'Invalid time range parameters'
        });
        return;
      }

      // Build analytics options
      const options = {
        timeRange,
        granularity: (req.query.granularity as 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year') || 'day',
        includeComparisons: req.query.includeComparisons === 'true',
        includePredictions: req.query.includePredictions === 'true',
        includeSegmentation: req.query.includeSegmentation === 'true'
      };

      // For user role, only allow their own data
      const targetUserId = user.role === RoleType.REGULAR_USER ? new mongoose.Types.ObjectId(user.id) : undefined;

      const report = await activityAnalyticsService.generateComprehensiveReport(
        options,
        targetUserId,
        user.role
      );

      res.status(200).json({
        success: true,
        data: report
      });

    } catch (error) {
      logger.error('Error generating analytics report:', error);
      res.status(500).json({
        success: false,
        message: 'Analytics report generation failed'
      });
    }
  }

  /**
   * Get user engagement dashboard
   */
  async getUserEngagementDashboard(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user || (user.role !== RoleType.ADMIN_USER && user.role !== RoleType.SUPER_ADMIN)) {
        res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
        return;
      }

      const timeRange = this.buildAnalyticsTimeRange(req.query);
      if (!timeRange) {
        res.status(400).json({
          success: false,
          message: 'Invalid time range parameters'
        });
        return;
      }

      const dashboard = await activityAnalyticsService.generateUserEngagementDashboard(
        timeRange,
        user.role
      );

      res.status(200).json({
        success: true,
        data: dashboard
      });

    } catch (error) {
      logger.error('Error generating user engagement dashboard:', error);
      res.status(500).json({
        success: false,
        message: 'User engagement dashboard generation failed'
      });
    }
  }

  /**
   * Get MyPts performance dashboard
   */
  async getMyPtsDashboard(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const timeRange = this.buildAnalyticsTimeRange(req.query);
      if (!timeRange) {
        res.status(400).json({
          success: false,
          message: 'Invalid time range parameters'
        });
        return;
      }

      // For user role, only allow their own data
      const targetUserId = user.role === RoleType.REGULAR_USER ? new mongoose.Types.ObjectId(user.id) : undefined;

      const dashboard = await activityAnalyticsService.generateMyPtsDashboard(
        timeRange,
        targetUserId
      );

      res.status(200).json({
        success: true,
        data: dashboard
      });

    } catch (error) {
      logger.error('Error generating MyPts dashboard:', error);
      res.status(500).json({
        success: false,
        message: 'MyPts dashboard generation failed'
      });
    }
  }

  /**
   * Get real-time analytics
   */
  async getRealTimeAnalytics(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user || (user.role !== RoleType.ADMIN_USER && user.role !== RoleType.SUPER_ADMIN)) {
        res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
        return;
      }

      const analytics = await activityAnalyticsService.generateRealTimeAnalytics();

      res.status(200).json({
        success: true,
        data: analytics
      });

    } catch (error) {
      logger.error('Error generating real-time analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Real-time analytics generation failed'
      });
    }
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(req: ActivityRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const partialInput = req.query.input as string;
      if (!partialInput || partialInput.length < 1) {
        res.status(400).json({
          success: false,
          message: 'Input parameter is required'
        });
        return;
      }

      const context = {
        userId: new mongoose.Types.ObjectId(user.id),
        userRole: user.role,
        allowedVisibility: this.getAllowedVisibility(user.role)
      };

      const limit = parseInt(req.query.limit as string) || 5;
      const suggestions = await activitySearchService.getSearchSuggestions(
        partialInput,
        context,
        limit
      );

      res.status(200).json({
        success: true,
        data: suggestions
      });

    } catch (error) {
      logger.error('Error getting search suggestions:', error);
      res.status(500).json({
        success: false,
        message: 'Search suggestions failed'
      });
    }
  }

  // ============================================================================
  // Helper Methods for Search and Analytics
  // ============================================================================

  private getAllowedVisibility(userRole: string): ActivityVisibility[] {
    switch (userRole) {
      case RoleType.SUPER_ADMIN:
      case RoleType.ADMIN_USER:
        return [
          ActivityVisibility.PUBLIC,
          ActivityVisibility.PRIVATE,
          ActivityVisibility.CONNECTIONS_ONLY,
          ActivityVisibility.ADMIN_ONLY
        ];
      case RoleType.REGULAR_USER:
      default:
        return [
          ActivityVisibility.PUBLIC,
          ActivityVisibility.PRIVATE,
          ActivityVisibility.CONNECTIONS_ONLY
        ];
    }
  }

  private buildAnalyticsTimeRange(query: any): { start: Date; end: Date; label?: string } | null {
    try {
      if (query.startDate && query.endDate) {
        return {
          start: new Date(query.startDate),
          end: new Date(query.endDate),
          label: 'custom'
        };
      }

      if (query.timeRange) {
        const now = new Date();
        const ranges: Record<string, { start: Date; end: Date; label: string }> = {
          today: {
            start: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
            end: now,
            label: 'today'
          },
          week: {
            start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
            end: now,
            label: 'last 7 days'
          },
          month: {
            start: new Date(now.getFullYear(), now.getMonth(), 1),
            end: now,
            label: 'this month'
          },
          quarter: {
            start: new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1),
            end: now,
            label: 'this quarter'
          },
          year: {
            start: new Date(now.getFullYear(), 0, 1),
            end: now,
            label: 'this year'
          }
        };

        return ranges[query.timeRange] || null;
      }

      // Default to last 30 days
      const now = new Date();
      return {
        start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        end: now,
        label: 'last 30 days'
      };

    } catch (error) {
      logger.error('Error building analytics time range:', error);
      return null;
    }
  }
}

export const activityLogsController = new ActivityLogsController();